<?php
/**
 * Enhanced Approval Admin Page
 *
 * This page provides a modern, user-friendly interface for administrators to manage approval workflows.
 */
if (!defined('ABSPATH')) exit;

global $wpdb;

$tables_table = $wpdb->prefix . 'dab_tables';
$fields_table = $wpdb->prefix . 'dab_fields';
$approval_levels_table = $wpdb->prefix . 'dab_approval_levels';

// Enqueue required styles and scripts
wp_enqueue_style('dab-approval-dashboard', plugin_dir_url(dirname(__FILE__)) . 'assets/css/approval-dashboard.css', array(), DAB_VERSION);
wp_enqueue_script('dab-approval-dashboard', plugin_dir_url(dirname(__FILE__)) . 'assets/js/approval-dashboard.js', array('jquery'), DAB_VERSION, true);

// Localize script with necessary variables
$approval_nonce = wp_create_nonce('dab_approval_nonce');
$general_nonce = wp_create_nonce('dab_nonce');
$current_user = wp_get_current_user();
wp_localize_script('dab-approval-dashboard', 'dab_vars', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => $general_nonce,
    'approval_nonce' => $approval_nonce,
    'current_user_name' => $current_user->display_name,
    'current_user_id' => $current_user->ID,
    'i18n' => array(
        'approve_success' => 'Record has been approved successfully.',
        'reject_success' => 'Record has been rejected successfully.',
        'error' => 'An error occurred. Please try again.'
    )
));



$message = '';
$current_user_id = get_current_user_id();

// Handle Approve/Reject Actions from form submission
if (isset($_POST['dab_approve_action']) && isset($_POST['record_id'], $_POST['table_id'])) {
    $action = sanitize_text_field($_POST['dab_approve_action']);
    $record_id = intval($_POST['record_id']);
    $table_id = intval($_POST['table_id']);
    $note = sanitize_textarea_field($_POST['approval_note'] ?? '');

    $status = ($action === 'approve') ? 'Approved' : 'Rejected';

    // Process the approval action
    if (DAB_Approval_Manager::can_user_approve($current_user_id, $table_id, $record_id)) {
        DAB_Approval_Manager::process_approval($table_id, $record_id, $status, $note, $current_user_id);
        $message = "Record has been " . strtolower($status) . " successfully.";
    } else {
        $message = "You don't have permission to approve this record at its current level.";
    }
}

// Process approval action if submitted via URL (from email link)
if (isset($_GET['action']) && isset($_GET['record_id']) && isset($_GET['table_id']) && isset($_GET['token'])) {
    $action = sanitize_text_field($_GET['action']);
    $record_id = intval($_GET['record_id']);
    $table_id = intval($_GET['table_id']);
    $token = sanitize_text_field($_GET['token']);

    // Verify the token
    if (wp_verify_nonce($token, 'dab_approval_' . $record_id)) {
        $status = ($action === 'approve') ? 'Approved' : 'Rejected';
        $note = "Action taken via email link by " . wp_get_current_user()->display_name;

        // Process the approval action
        if (DAB_Approval_Manager::can_user_approve($current_user_id, $table_id, $record_id)) {
            DAB_Approval_Manager::process_approval($table_id, $record_id, $status, $note, $current_user_id);
            $message = "Record has been " . strtolower($status) . " successfully via email link.";

            // Add JavaScript to show a modal for adding notes
            add_action('admin_footer', function() use ($record_id, $table_id, $status) {
                ?>
                <script>
                jQuery(document).ready(function($) {
                    // Show modal for adding notes
                    $('#dab-approval-action').val('<?php echo $status === 'Approved' ? 'approve' : 'reject'; ?>');
                    $('#dab-approval-record-id').val('<?php echo $record_id; ?>');
                    $('#dab-approval-table-id').val('<?php echo $table_id; ?>');
                    $('#dab-approval-modal-title').text('<?php echo $status; ?> Record');
                    $('#dab-approval-note').val('<?php echo "Action taken via email link"; ?>');

                    // Show appropriate button
                    $('.dab-modal-approve-btn, .dab-modal-reject-btn').hide();
                    if ('<?php echo $status; ?>' === 'Approved') {
                        $('.dab-modal-approve-btn').show();
                    } else {
                        $('.dab-modal-reject-btn').show();
                    }

                    // Make sure the record title is set properly
                    const recordTitle = 'Record #' + <?php echo $record_id; ?>;
                    $('#dab-approval-record-title').text(recordTitle);

                    // Show modal
                    $('#dab-approval-modal').css('display', 'flex').show();
                    $('.dab-modal-overlay').show();
                });
                </script>
                <?php
            });
        } else {
            $message = "You don't have permission to approve this record at its current level.";
        }
    } else {
        $message = "Invalid security token. Please try again.";
    }
}

// Get tables with approval workflows
$tables_with_approval = $wpdb->get_results(
    "SELECT DISTINCT t.*
    FROM {$wpdb->prefix}dab_tables t
    INNER JOIN {$wpdb->prefix}dab_approval_levels l ON t.id = l.table_id
    ORDER BY t.table_label"
);

$selected_table_id = isset($_GET['table_id']) ? intval($_GET['table_id']) : 0;

// Get approval stats
$total_pending = 0;
$total_approved = 0;
$total_rejected = 0;
$user_pending = 0; // Count of records specifically assigned to current user

// Check if user is admin
$is_admin = current_user_can('administrator');

foreach ($tables_with_approval as $table) {
    $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);

    if ($is_admin) {
        // Administrators see all records
        $pending = $wpdb->get_var("SELECT COUNT(*) FROM $data_table WHERE approval_status = 'Pending'");
        $approved = $wpdb->get_var("SELECT COUNT(*) FROM $data_table WHERE approval_status = 'Approved'");
        $rejected = $wpdb->get_var("SELECT COUNT(*) FROM $data_table WHERE approval_status = 'Rejected'");

        // For admins, all pending are also user_pending
        $user_pending += $pending;
    } else {
        // For non-admins, count records they can approve
        $pending_records = $wpdb->get_results(
            "SELECT id FROM $data_table WHERE approval_status = 'Pending' AND current_approval_level > 0"
        );

        $table_user_pending = 0;
        foreach ($pending_records as $record) {
            if (DAB_Approval_Manager::can_user_approve($current_user_id, $table->id, $record->id)) {
                $table_user_pending++;
            }
        }

        // For non-admins, only count records they can approve
        $pending = $table_user_pending;
        $user_pending += $table_user_pending;

        // For approved/rejected, only show records the user submitted or was involved in approving
        $user_records = $wpdb->get_col($wpdb->prepare(
            "SELECT id FROM $data_table WHERE user_id = %d",
            $current_user_id
        ));

        // Get approval history for this user
        $approval_history_table = $wpdb->prefix . 'dab_approval_history';
        $user_approved_records = $wpdb->get_col($wpdb->prepare(
            "SELECT DISTINCT record_id FROM $approval_history_table
            WHERE user_id = %d AND table_id = %d",
            $current_user_id, $table->id
        ));

        // Combine user's records and records they approved
        $user_related_records = array_unique(array_merge($user_records, $user_approved_records));

        if (!empty($user_related_records)) {
            $record_ids = implode(',', array_map('intval', $user_related_records));
            $approved = $wpdb->get_var(
                "SELECT COUNT(*) FROM $data_table
                WHERE approval_status = 'Approved'
                AND id IN ($record_ids)"
            );

            $rejected = $wpdb->get_var(
                "SELECT COUNT(*) FROM $data_table
                WHERE approval_status = 'Rejected'
                AND id IN ($record_ids)"
            );
        } else {
            $approved = 0;
            $rejected = 0;
        }
    }

    $total_pending += $pending;
    $total_approved += $approved;
    $total_rejected += $rejected;
}

// Get records pending approval that the current user can approve
$pending_records = [];
if ($selected_table_id) {
    $table = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $tables_table WHERE id = %d",
        $selected_table_id
    ));

    if ($table) {
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);

        // Get fields for this table
        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $fields_table WHERE table_id = %d ORDER BY field_order",
            $selected_table_id
        ));

        // Check if user is admin
        $is_admin = current_user_can('administrator');

        // Get all records with any approval status
        $all_records = $wpdb->get_results(
            "SELECT * FROM $data_table
            WHERE approval_status IN ('Pending', 'Approved', 'Rejected')
            ORDER BY id DESC"
        );

        // For admins, show all records
        if ($is_admin) {
            $pending_records = $all_records;
        } else {
            // For non-admins, filter records they can approve or have approved/rejected
            foreach ($all_records as $record) {
                // Include pending records they can approve
                if ($record->approval_status === 'Pending' &&
                    DAB_Approval_Manager::can_user_approve($current_user_id, $selected_table_id, $record->id)) {
                    $pending_records[] = $record;
                }
                // Include approved/rejected records they were involved with
                else if (($record->approval_status === 'Approved' || $record->approval_status === 'Rejected') &&
                         ($record->approved_by == $current_user_id || $record->rejected_by == $current_user_id)) {
                    $pending_records[] = $record;
                }
            }
        }
    }
}
?>

<!-- Include approval modal template -->
<?php include_once(plugin_dir_path(dirname(__FILE__)) . 'templates/approval-modal.php'); ?>

<div class="wrap">
    <div class="dab-approval-dashboard">
        <div class="dab-dashboard-header">
            <h1 class="dab-dashboard-title">
                <span class="dashicons dashicons-clock" style="margin-right: 8px;"></span>
                <?php _e('Pending Approvals', 'db-app-builder'); ?>
            </h1>
            <p class="dab-dashboard-subtitle" style="margin-bottom: 20px; font-size: 14px; color: #666;">
                <?php _e('Manage approval tasks assigned to you. Review, approve, or reject records that require your authorization.', 'db-app-builder'); ?>
            </p>

            <!-- Display stats -->
            <div class="dab-dashboard-stats">
                <div class="dab-stat-card dab-stat-total">
                    <div class="dab-stat-number" id="dab-count-total"><?php echo ($total_pending + $total_approved + $total_rejected); ?></div>
                    <div class="dab-stat-label">Total Records</div>
                </div>

                <div class="dab-stat-card dab-stat-pending">
                    <div class="dab-stat-number" id="dab-count-pending"><?php echo $total_pending; ?></div>
                    <div class="dab-stat-label">Pending</div>
                    <?php if (!$is_admin && $user_pending > 0): ?>
                    <div class="dab-stat-user-pending"><?php echo $user_pending; ?> assigned to you</div>
                    <?php elseif ($is_admin): ?>
                    <div class="dab-stat-admin-access">Admin access to all</div>
                    <?php endif; ?>
                </div>

                <div class="dab-stat-card dab-stat-approved">
                    <div class="dab-stat-number" id="dab-count-approved"><?php echo $total_approved; ?></div>
                    <div class="dab-stat-label">Approved</div>
                </div>

                <div class="dab-stat-card dab-stat-rejected">
                    <div class="dab-stat-number" id="dab-count-rejected"><?php echo $total_rejected; ?></div>
                    <div class="dab-stat-label">Rejected</div>
                </div>
            </div>
        </div>

        <?php if ($message): ?>
            <div class="notice notice-success is-dismissible"><p><?php echo esc_html($message); ?></p></div>
        <?php endif; ?>

        <div class="dab-dashboard-filters">
            <div class="dab-filter-group">
                <label class="dab-filter-label">Table:</label>
                <form method="get" style="display:inline;">
                    <input type="hidden" name="page" value="dab_approvals">
                    <select name="table_id" class="dab-filter-select" onchange="this.form.submit();" style="padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px; min-width: 200px; position: relative; z-index: 100; height: auto; appearance: menulist; -webkit-appearance: menulist; -moz-appearance: menulist;">
                        <option value="">-- Select Table --</option>
                        <?php foreach ($tables_with_approval as $table): ?>
                            <option value="<?php echo esc_attr($table->id); ?>" <?php selected($selected_table_id, $table->id); ?>>
                                <?php echo esc_html($table->table_label); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </form>
            </div>

            <?php if ($selected_table_id): ?>
                <div class="dab-filter-group">
                    <label class="dab-filter-label">Status:</label>
                    <select id="dab-filter-status" class="dab-filter-select">
                        <option value="all" selected>All</option>
                        <option value="Pending">Pending</option>
                        <option value="Approved">Approved</option>
                        <option value="Rejected">Rejected</option>
                    </select>
                </div>

                <div class="dab-filter-search">
                    <input type="text" id="dab-approval-search" placeholder="Search records...">
                </div>
            <?php endif; ?>
        </div>

        <?php if ($selected_table_id && !empty($pending_records)): ?>
            <div class="dab-approval-records">
                <?php foreach ($pending_records as $record): ?>
                    <?php
                    // Get primary field for record title
                    $primary_field = reset($fields);
                    $record_title = $record->{$primary_field->field_slug} ?? 'Record #' . $record->id;

                    // Format date
                    $created_date = !empty($record->created_at) ? date('Y-m-d', strtotime($record->created_at)) : '';

                    // Get submitter info
                    $submitter = get_userdata($record->user_id);
                    $submitter_name = $submitter ? $submitter->display_name : 'Unknown';

                    // Get current level
                    $level = DAB_Approval_Manager::get_current_level_info($selected_table_id, $record->id);
                    $level_name = $level ? $level->level_name : 'Initial Review';
                    ?>

                    <?php
                    // Check if this record is directly assigned to the current user (not just admin access)
                    $is_assigned = !$is_admin && DAB_Approval_Manager::can_user_approve($current_user_id, $selected_table_id, $record->id);
                    ?>
                    <div class="dab-approval-record"
                         data-status="<?php echo esc_attr($record->approval_status); ?>"
                         data-date="<?php echo esc_attr($created_date); ?>"
                         data-assigned="<?php echo $is_assigned ? 'true' : 'false'; ?>"
                         data-record-id="<?php echo esc_attr($record->id); ?>"
                         data-table-id="<?php echo esc_attr($selected_table_id); ?>">
                        <!-- Record header -->
                        <div class="dab-record-header">
                            <div>
                                <h4 class="dab-record-title"><?php echo esc_html($record_title); ?></h4>
                                <div class="dab-record-meta">
                                    <div class="dab-record-submitter">Submitted by: <?php echo esc_html($submitter_name); ?></div>
                                    <div class="dab-record-date">Date: <?php echo esc_html($created_date); ?></div>
                                    <div class="dab-record-level">Current Level: <?php echo esc_html($level_name); ?></div>
                                    <?php if ($record->approval_status === 'Approved' && !empty($record->approved_at)): ?>
                                        <div class="dab-record-approved-date">Approved on: <?php echo esc_html(date('F j, Y g:i a', strtotime($record->approved_at))); ?></div>
                                    <?php endif; ?>
                                    <?php if ($record->approval_status === 'Rejected' && !empty($record->rejected_at)): ?>
                                        <div class="dab-record-rejected-date">Rejected on: <?php echo esc_html(date('F j, Y g:i a', strtotime($record->rejected_at))); ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Status badge -->
                            <div class="dab-approval-status dab-status-<?php echo strtolower($record->approval_status); ?>" data-status="<?php echo esc_attr($record->approval_status); ?>">
                                <?php if ($record->approval_status === 'Pending'): ?>
                                    <?php if ($is_assigned): ?>
                                        Assigned to You
                                    <?php elseif ($is_admin): ?>
                                        Admin Access
                                    <?php else: ?>
                                        Pending
                                    <?php endif; ?>
                                <?php elseif ($record->approval_status === 'Approved'): ?>
                                    Approved
                                <?php elseif ($record->approval_status === 'Rejected'): ?>
                                    Rejected
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Record content -->
                        <div class="dab-record-content">
                            <?php
                            // Show up to 5 important fields
                            $display_fields = array_slice($fields, 0, 5);
                            foreach ($display_fields as $field):
                                $field_value = $record->{$field->field_slug} ?? '';

                                // Format field value based on type
                                if ($field->field_type === 'lookup') {
                                    $lookup_table_id = intval($field->lookup_table_id);
                                    $display_column = sanitize_text_field($field->lookup_display_column);
                                    if ($lookup_table_id && $display_column) {
                                        $lookup_table_slug = $wpdb->get_var(
                                            $wpdb->prepare("SELECT table_slug FROM {$wpdb->prefix}dab_tables WHERE id = %d", $lookup_table_id)
                                        );
                                        if ($lookup_table_slug) {
                                            $lookup_table = $wpdb->prefix . 'dab_' . sanitize_title($lookup_table_slug);
                                            $lookup_id = intval($field_value);
                                            $field_value = $wpdb->get_var(
                                                $wpdb->prepare("SELECT `$display_column` FROM `$lookup_table` WHERE id = %d", $lookup_id)
                                            );
                                        }
                                    }
                                } elseif ($field->field_type === 'date') {
                                    $field_value = !empty($field_value) ? date('F j, Y', strtotime($field_value)) : '';
                                } elseif ($field->field_type === 'datetime') {
                                    $field_value = !empty($field_value) ? date('F j, Y g:i a', strtotime($field_value)) : '';
                                } elseif ($field->field_type === 'boolean' || $field->field_type === 'checkbox') {
                                    $field_value = !empty($field_value) ? '<span class="dashicons dashicons-yes" style="color: green;"></span>' : '<span class="dashicons dashicons-no" style="color: red;"></span>';
                                }

                                // Skip empty values
                                if (empty($field_value) && $field_value !== '0') continue;
                            ?>
                                <div class="dab-record-field">
                                    <div class="dab-field-label"><?php echo esc_html($field->field_label); ?>:</div>
                                    <div class="dab-field-value"><?php echo $field->field_type === 'boolean' || $field->field_type === 'checkbox' ? $field_value : esc_html($field_value); ?></div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Record actions -->
                        <div class="dab-record-actions">
                            <button type="button" class="dab-action-btn dab-view-btn dab-view-record-btn"
                                    data-record="<?php echo esc_attr($record->id); ?>"
                                    data-table="<?php echo esc_attr($selected_table_id); ?>"
                                    data-title="<?php echo esc_attr($record_title); ?>">
                                View Details
                            </button>

                            <?php if ($record->approval_status === 'Pending'): ?>
                            <!-- For pending records, show both approve and reject buttons -->
                            <button type="button" class="dab-action-btn dab-approve-btn"
                                    data-record="<?php echo esc_attr($record->id); ?>"
                                    data-table="<?php echo esc_attr($selected_table_id); ?>"
                                    data-title="<?php echo esc_attr($record_title); ?>">
                                Approve
                            </button>

                            <button type="button" class="dab-action-btn dab-reject-btn"
                                    data-record="<?php echo esc_attr($record->id); ?>"
                                    data-table="<?php echo esc_attr($selected_table_id); ?>"
                                    data-title="<?php echo esc_attr($record_title); ?>">
                                Reject
                            </button>
                            <?php elseif ($record->approval_status === 'Approved'): ?>
                            <!-- For approved records, only show reject button -->
                            <button type="button" class="dab-action-btn dab-reject-btn"
                                    data-record="<?php echo esc_attr($record->id); ?>"
                                    data-table="<?php echo esc_attr($selected_table_id); ?>"
                                    data-title="<?php echo esc_attr($record_title); ?>">
                                Reject
                            </button>
                            <?php elseif ($record->approval_status === 'Rejected'): ?>
                            <!-- For rejected records, only show approve button -->
                            <button type="button" class="dab-action-btn dab-approve-btn"
                                    data-record="<?php echo esc_attr($record->id); ?>"
                                    data-table="<?php echo esc_attr($selected_table_id); ?>"
                                    data-title="<?php echo esc_attr($record_title); ?>">
                                Approve
                            </button>
                            <?php endif; ?>

                            <?php if ($record->approval_status === 'Approved'): ?>
                                <span class="dab-approval-info">
                                    <?php
                                    $approver = get_userdata($record->approved_by);
                                    $approver_name = $approver ? $approver->display_name : 'Unknown';
                                    echo 'Approved by: ' . esc_html($approver_name);
                                    ?>
                                </span>
                            <?php elseif ($record->approval_status === 'Rejected'): ?>
                                <span class="dab-approval-info">
                                    <?php
                                    $rejecter = get_userdata($record->rejected_by);
                                    $rejecter_name = $rejecter ? $rejecter->display_name : 'Unknown';
                                    echo 'Rejected by: ' . esc_html($rejecter_name);
                                    ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php elseif ($selected_table_id): ?>
            <div class="dab-empty-state">
                <div class="dab-empty-icon">📋</div>
                <h3>No Records Found</h3>
                <p>There are no records in this table that match your current filter criteria.</p>
            </div>
        <?php else: ?>
            <div class="dab-empty-state">
                <div class="dab-empty-icon">📊</div>
                <h3>Select a Table to View Approvals</h3>
                <p>Choose a table from the dropdown above to view records pending your approval.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
/* Empty state styling */
.dab-empty-state {
    text-align: center;
    padding: 50px 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin: 20px 0;
}

.dab-empty-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.dab-empty-state h3 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #343a40;
}

.dab-empty-state p {
    font-size: 16px;
    color: #6c757d;
    max-width: 500px;
    margin: 0 auto;
}

/* User-specific approval styling */
.dab-stat-user-pending {
    font-size: 12px;
    font-weight: bold;
    color: #ff5722;
    margin-top: 5px;
}

.dab-stat-admin-access {
    font-size: 12px;
    font-weight: bold;
    color: #2196f3;
    margin-top: 5px;
}

/* Highlight records assigned to current user */
.dab-approval-record[data-assigned="true"] {
    border-left: 4px solid #4caf50;
    background-color: #f1f8e9;
}

/* Approval status styling */
.dab-approval-status {
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: bold;
    text-align: center;
    min-width: 120px;
}

.dab-status-pending {
    background-color: #ff9800;
    color: white;
}

.dab-status-approved {
    background-color: #4caf50;
    color: white;
}

.dab-status-rejected {
    background-color: #f44336;
    color: white;
}

/* Make buttons more visible */
.dab-action-btn {
    padding: 8px 16px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-weight: bold;
    margin-right: 8px;
    transition: background-color 0.3s;
}

.dab-approve-btn {
    background-color: #4caf50;
    color: white;
}

.dab-approve-btn:hover {
    background-color: #388e3c;
}

.dab-reject-btn {
    background-color: #f44336;
    color: white;
}

.dab-reject-btn:hover {
    background-color: #d32f2f;
}

.dab-view-btn {
    background-color: #2196f3;
    color: white;
}

.dab-view-btn:hover {
    background-color: #1976d2;
}

/* Approval info styling */
.dab-approval-info {
    display: inline-block;
    padding: 8px 16px;
    background-color: #f5f5f5;
    border-radius: 4px;
    color: #555;
    font-style: italic;
    margin-left: 10px;
    vertical-align: middle;
}

/* Approval date styling */
.dab-record-approved-date, .dab-record-rejected-date {
    font-style: italic;
    color: #666;
    margin-top: 4px;
}
</style>
