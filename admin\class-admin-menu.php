<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_Admin_Menu {

    public function __construct() {
        add_action('admin_menu', array($this, 'register_menu'));
    }

    public function register_menu() {
        add_menu_page(
            __('Database App Builder', 'db-app-builder'),
            __('Database App Builder', 'db-app-builder'),
            'manage_options',
            'dab_dashboard',
            array($this, 'load_dashboard_page'),
            'dashicons-database',
            25
        );

        // ===== GROUP 1: OVERVIEW & QUICK START =====
        add_submenu_page(
            'dab_dashboard',
            __('Dashboard', 'db-app-builder'),
            __('📊 Dashboard', 'db-app-builder'),
            'manage_options',
            'dab_dashboard',
            array($this, 'load_dashboard_page')
        );

        // App Templates - moved here to replace Quick Start Wizards
        add_submenu_page(
            'dab_dashboard',
            __('App Templates', 'db-app-builder'),
            __('🏗️ App Templates', 'db-app-builder'),
            'manage_options',
            'dab_app_templates',
            array($this, 'load_app_templates_page')
        );

        // Separator: Overview & Quick Start
        add_submenu_page(
            'dab_dashboard',
            '',
            '<div class="dab-menu-separator" data-group="overview">
                <span class="dab-menu-separator-icon dashicons dashicons-admin-home"></span>
                <span class="dab-menu-separator-text">Overview & Quick Start</span>
            </div>',
            'manage_options',
            'dab_separator_overview',
            '__return_false'
        );

        // Separator: Data Foundation
        add_submenu_page(
            'dab_dashboard',
            '',
            '<div class="dab-menu-separator" data-group="data-foundation">
                <span class="dab-menu-separator-icon dashicons dashicons-database-view"></span>
                <span class="dab-menu-separator-text">Data Foundation</span>
            </div>',
            'manage_options',
            'dab_separator_data_foundation',
            '__return_false'
        );

        // ===== GROUP 2: DATA FOUNDATION =====
        add_submenu_page(
            'dab_dashboard',
            __('Tables', 'db-app-builder'),
            __('🗃️ Tables', 'db-app-builder'),
            'manage_options',
            'dab_tables',
            array($this, 'load_tables_page')
        );

        add_submenu_page(
            'dab_dashboard',
            __('Fields', 'db-app-builder'),
            __('📝 Fields', 'db-app-builder'),
            'manage_options',
            'dab_fields',
            array($this, 'load_fields_page')
        );

        add_submenu_page(
            'dab_dashboard',
            __('Relationships', 'db-app-builder'),
            __('🔗 Relationships', 'db-app-builder'),
            'manage_options',
            'dab_relationships',
            array($this, 'load_relationships_page')
        );

        // Separator: Application Interface
        add_submenu_page(
            'dab_dashboard',
            '',
            '<div class="dab-menu-separator" data-group="app-interface">
                <span class="dab-menu-separator-icon dashicons dashicons-layout"></span>
                <span class="dab-menu-separator-text">Application Interface</span>
            </div>',
            'manage_options',
            'dab_separator_app_interface',
            '__return_false'
        );

        // ===== GROUP 3: APPLICATION INTERFACE =====
        add_submenu_page(
            'dab_dashboard',
            __('Forms', 'db-app-builder'),
            __('📋 Forms', 'db-app-builder'),
            'manage_options',
            'dab_forms',
            array($this, 'load_forms_page')
        );

        add_submenu_page(
            'dab_dashboard',
            __('Views', 'db-app-builder'),
            __('👁️ Views', 'db-app-builder'),
            'manage_options',
            'dab_views',
            array($this, 'load_views_page')
        );

        add_submenu_page(
            'dab_dashboard',
            __('Dashboards', 'db-app-builder'),
            __('📈 Dashboards', 'db-app-builder'),
            'manage_options',
            'dab_dashboards',
            array($this, 'load_dashboards_page')
        );

        // Separator: Workflow & Automation
        add_submenu_page(
            'dab_dashboard',
            '',
            '<div class="dab-menu-separator" data-group="workflow-automation">
                <span class="dab-menu-separator-icon dashicons dashicons-networking"></span>
                <span class="dab-menu-separator-text">Workflow & Automation</span>
            </div>',
            'manage_options',
            'dab_separator_workflow_automation',
            '__return_false'
        );

        // ===== GROUP 4: WORKFLOW & AUTOMATION =====
        // General automation engine
        add_submenu_page(
            'dab_dashboard',
            __('Workflow Builder', 'db-app-builder'),
            __('⚙️ Workflow Builder', 'db-app-builder'),
            'manage_options',
            'dab_workflows',
            array($this, 'load_workflows_page')
        );

        // Approval-specific workflow setup
        add_submenu_page(
            'dab_dashboard',
            __('Approval Workflows', 'db-app-builder'),
            __('✅ Approval Workflows', 'db-app-builder'),
            'manage_options',
            'dab_approval_config',
            array($this, 'load_approval_config_page')
        );

        // Approval task management interface
        add_submenu_page(
            'dab_dashboard',
            __('Pending Approvals', 'db-app-builder'),
            __('⏳ Pending Approvals', 'db-app-builder'),
            'edit_posts', // Lower permission level so non-admins can approve
            'dab_approvals',
            array($this, 'load_approvals_page')
        );

        // Separator: Data Management
        add_submenu_page(
            'dab_dashboard',
            '',
            '<div class="dab-menu-separator" data-group="data-management">
                <span class="dab-menu-separator-icon dashicons dashicons-database-add"></span>
                <span class="dab-menu-separator-text">Data Management</span>
            </div>',
            'manage_options',
            'dab_separator_data_management',
            '__return_false'
        );

        // ===== GROUP 5: DATA MANAGEMENT =====
        add_submenu_page(
            'dab_dashboard',
            __('Data Management', 'db-app-builder'),
            __('📊 Data Management', 'db-app-builder'),
            'edit_posts', // Allow editors and above
            'dab_data_dashboard',
            array($this, 'load_data_dashboard_page')
        );

        // Separator: User Management & Communication
        add_submenu_page(
            'dab_dashboard',
            '',
            '<div class="dab-menu-separator" data-group="user-management">
                <span class="dab-menu-separator-icon dashicons dashicons-groups"></span>
                <span class="dab-menu-separator-text">User Management & Communication</span>
            </div>',
            'manage_options',
            'dab_separator_user_management',
            '__return_false'
        );

        // ===== GROUP 6: USER MANAGEMENT & COMMUNICATION =====
        add_submenu_page(
            'dab_dashboard',
            __('Frontend Users', 'db-app-builder'),
            __('👥 Frontend Users', 'db-app-builder'),
            'manage_options',
            'dab_frontend_users',
            array($this, 'load_frontend_users_page')
        );

        add_submenu_page(
            'dab_dashboard',
            __('Chat Groups', 'db-app-builder'),
            __('💬 Chat Groups', 'db-app-builder'),
            'manage_options',
            'dab_chat_groups',
            array($this, 'load_chat_groups_page')
        );

        add_submenu_page(
            'dab_dashboard',
            __('Permissions Dashboard', 'db-app-builder'),
            __('🔐 Permissions Dashboard', 'db-app-builder'),
            'manage_options',
            'dab_permissions_dashboard',
            array($this, 'load_permissions_dashboard_page')
        );

        // Separator: Analytics & Intelligence
        add_submenu_page(
            'dab_dashboard',
            '',
            '<div class="dab-menu-separator" data-group="analytics">
                <span class="dab-menu-separator-icon dashicons dashicons-chart-bar"></span>
                <span class="dab-menu-separator-text">Analytics & Intelligence</span>
            </div>',
            'manage_options',
            'dab_separator_analytics',
            '__return_false'
        );

        // ===== GROUP 7: ANALYTICS & INTELLIGENCE =====
        add_submenu_page(
            'dab_dashboard',
            __('Report Builder', 'db-app-builder'),
            __('📊 Report Builder', 'db-app-builder'),
            'edit_posts',
            'dab_report_builder',
            array($this, 'load_report_builder_page')
        );

        add_submenu_page(
            'dab_dashboard',
            __('Analytics Dashboard', 'db-app-builder'),
            __('📈 Analytics Dashboard', 'db-app-builder'),
            'edit_posts',
            'dab_analytics_dashboard',
            array($this, 'load_analytics_dashboard_page')
        );

        add_submenu_page(
            'dab_dashboard',
            __('Data Insights', 'db-app-builder'),
            __('🔍 Data Insights', 'db-app-builder'),
            'edit_posts',
            'dab_data_insights',
            array($this, 'load_data_insights_page')
        );

        add_submenu_page(
            'dab_dashboard',
            __('Scheduled Reports', 'db-app-builder'),
            __('⏰ Scheduled Reports', 'db-app-builder'),
            'manage_options',
            'dab_scheduled_reports',
            array($this, 'load_scheduled_reports_page')
        );

        // WooCommerce Integration submenu items
        if (class_exists('WooCommerce')) {
            // Separator: WooCommerce Integration
            add_submenu_page(
                'dab_dashboard',
                '',
                '<div class="dab-menu-separator" data-group="woocommerce">
                    <span class="dab-menu-separator-icon dashicons dashicons-cart"></span>
                    <span class="dab-menu-separator-text">WooCommerce Integration</span>
                </div>',
                'manage_options',
                'dab_separator_woocommerce',
                '__return_false'
            );

            // ===== GROUP 8: WOOCOMMERCE INTEGRATION =====
            add_submenu_page(
                'dab_dashboard',
                __('WooCommerce Sales Dashboard', 'db-app-builder'),
                __('🛒 Sales Dashboard', 'db-app-builder'),
                'manage_options',
                'dab_woocommerce_sales_dashboard',
                array($this, 'load_woocommerce_sales_dashboard_page')
            );

            add_submenu_page(
                'dab_dashboard',
                __('WooCommerce Product Fields', 'db-app-builder'),
                __('📦 Product Fields', 'db-app-builder'),
                'manage_options',
                'dab_woocommerce_product_fields',
                array($this, 'load_woocommerce_product_fields_page')
            );

            add_submenu_page(
                'dab_dashboard',
                __('WooCommerce Order Fields', 'db-app-builder'),
                __('📋 Order Fields', 'db-app-builder'),
                'manage_options',
                'dab_woocommerce_order_fields',
                array($this, 'load_woocommerce_order_fields_page')
            );

            add_submenu_page(
                'dab_dashboard',
                __('WooCommerce Customer Data', 'db-app-builder'),
                __('👤 Customer Data', 'db-app-builder'),
                'manage_options',
                'dab_woocommerce_customer_data',
                array($this, 'load_woocommerce_customer_data_page')
            );

            add_submenu_page(
                'dab_dashboard',
                __('WooCommerce Inventory Management', 'db-app-builder'),
                __('📊 Inventory Management', 'db-app-builder'),
                'manage_options',
                'dab_woocommerce_inventory',
                array($this, 'load_woocommerce_inventory_page')
            );

            add_submenu_page(
                'dab_dashboard',
                __('WooCommerce Advanced Checkout', 'db-app-builder'),
                __('💳 Advanced Checkout', 'db-app-builder'),
                'manage_options',
                'dab_woocommerce_advanced_checkout',
                array($this, 'load_woocommerce_advanced_checkout_page')
            );

            add_submenu_page(
                'dab_dashboard',
                __('WooCommerce Marketing Automation', 'db-app-builder'),
                __('📧 Marketing Automation', 'db-app-builder'),
                'manage_options',
                'dab_woocommerce_marketing',
                array($this, 'load_woocommerce_marketing_page')
            );

            add_submenu_page(
                'dab_dashboard',
                __('WooCommerce Enhanced Reports', 'db-app-builder'),
                __('📈 Enhanced Reports', 'db-app-builder'),
                'manage_options',
                'dab_woocommerce_enhanced_reports',
                array($this, 'load_woocommerce_enhanced_reports_page')
            );
        }

        // Separator: Integrations & Payments
        add_submenu_page(
            'dab_dashboard',
            '',
            '<div class="dab-menu-separator" data-group="integrations">
                <span class="dab-menu-separator-icon dashicons dashicons-admin-plugins"></span>
                <span class="dab-menu-separator-text">Integrations & Payments</span>
            </div>',
            'manage_options',
            'dab_separator_integrations',
            '__return_false'
        );

        // ===== GROUP 9: INTEGRATIONS & PAYMENTS =====
        add_submenu_page(
            'dab_dashboard',
            __('Payment Settings', 'db-app-builder'),
            __('💰 Payment Settings', 'db-app-builder'),
            'manage_options',
            'dab_payment_settings',
            array($this, 'load_payment_settings_page')
        );

        add_submenu_page(
            'dab_dashboard',
            __('Payment Tracking', 'db-app-builder'),
            __('💳 Payment Tracking', 'db-app-builder'),
            'manage_options',
            'dab_payment_tracking',
            array($this, 'load_payment_tracking_page')
        );

        add_submenu_page(
            'dab_dashboard',
            __('Integrations', 'db-app-builder'),
            __('🔌 Integrations', 'db-app-builder'),
            'manage_options',
            'dab_integrations',
            array($this, 'load_integrations_page')
        );

        // Separator: Configuration & Settings
        add_submenu_page(
            'dab_dashboard',
            '',
            '<div class="dab-menu-separator" data-group="configuration">
                <span class="dab-menu-separator-icon dashicons dashicons-admin-settings"></span>
                <span class="dab-menu-separator-text">Configuration & Settings</span>
            </div>',
            'manage_options',
            'dab_separator_configuration',
            '__return_false'
        );

        // ===== GROUP 10: CONFIGURATION & SETTINGS =====
        add_submenu_page(
            'dab_dashboard',
            __('Settings', 'db-app-builder'),
            __('⚙️ Settings', 'db-app-builder'),
            'manage_options',
            'dab_settings',
            array($this, 'load_settings_page')
        );

        add_submenu_page(
            'dab_dashboard',
            __('License', 'db-app-builder'),
            __('🔑 License', 'db-app-builder'),
            'manage_options',
            'dab_license',
            array($this, 'load_license_page')
        );

        add_submenu_page(
            'dab_dashboard',
            __('Uninstall Settings', 'db-app-builder'),
            __('🗑️ Uninstall Settings', 'db-app-builder'),
            'manage_options',
            'dab_uninstall_settings',
            array($this, 'load_uninstall_settings_page')
        );

        // Separator: Documentation & Help
        add_submenu_page(
            'dab_dashboard',
            '',
            '<div class="dab-menu-separator" data-group="documentation">
                <span class="dab-menu-separator-icon dashicons dashicons-book"></span>
                <span class="dab-menu-separator-text">Documentation & Help</span>
            </div>',
            'manage_options',
            'dab_separator_documentation',
            '__return_false'
        );

        // ===== GROUP 11: DOCUMENTATION & HELP =====
        add_submenu_page(
            'dab_dashboard',
            __('Documentation', 'db-app-builder'),
            __('📚 Documentation', 'db-app-builder'),
            'manage_options',
            'dab_documentation',
            array($this, 'load_documentation_page')
        );

        add_submenu_page(
            'dab_dashboard',
            __('Permissions Docs', 'db-app-builder'),
            __('🔐 Permissions Docs', 'db-app-builder'),
            'manage_options',
            'dab_permissions_docs',
            array($this, 'load_permissions_docs_page')
        );

        // Hidden pages (not in menu)
        add_submenu_page(
            null, // No parent - hidden from menu
            __('Dashboard Builder', 'db-app-builder'),
            __('Dashboard Builder', 'db-app-builder'),
            'manage_options',
            'dab_dashboard_builder',
            array($this, 'load_dashboard_builder_page')
        );

        add_submenu_page(
            null, // No parent - hidden from menu
            __('Dashboard View', 'db-app-builder'),
            __('Dashboard View', 'db-app-builder'),
            'edit_posts', // Allow editors and above
            'dab_dashboard_view',
            array($this, 'load_dashboard_view_page')
        );

        add_submenu_page(
            null, // No parent - hidden from menu
            __('Create Application Wizard', 'db-app-builder'),
            __('Create Application Wizard', 'db-app-builder'),
            'manage_options',
            'dab_wizard_app_creation',
            array($this, 'load_wizard_app_creation_page')
        );

        add_submenu_page(
            null, // No parent - hidden from menu
            __('Setup Workflow Wizard', 'db-app-builder'),
            __('Setup Workflow Wizard', 'db-app-builder'),
            'manage_options',
            'dab_wizard_workflow_setup',
            array($this, 'load_wizard_workflow_setup_page')
        );

        // Add CSS for menu separators
        add_action('admin_head', array($this, 'add_admin_menu_separator_styles'));
    }

    /**
     * Add CSS styles for menu separators
     */
    public function add_admin_menu_separator_styles() {
        echo '<style>
            /* Menu separator styling */
            .dab-menu-separator {
                display: flex;
                align-items: center;
                margin: 5px 0 2px 0;
                padding: 0;
                height: auto;
                line-height: 1.4;
                font-size: 12px;
                font-weight: 700;
                text-transform: uppercase;
                color: #f0f0f0; /* Lighter color for better visibility */
                border-bottom: 1px solid rgba(240, 240, 240, 0.2);
                background: transparent !important;
                letter-spacing: 0.5px;
                cursor: pointer;
            }

            /* Icon styling */
            .dab-menu-separator-icon {
                margin-right: 5px;
                font-size: 14px;
                transition: transform 0.2s ease;
            }

            /* Rotated icon for collapsed state */
            .dab-menu-separator.collapsed .dab-menu-separator-icon {
                transform: rotate(-90deg);
            }

            /* Fix for WordPress admin menu */
            #adminmenu a.menu-top.dab-menu-separator {
                cursor: pointer;
            }

            /* Hover effect for the separator */
            #adminmenu a:hover .dab-menu-separator {
                color: #00b9eb; /* WordPress blue on hover */
            }

            /* Adjust spacing for better visual hierarchy */
            #adminmenu li a[href*="dab_separator_"] {
                padding-top: 4px;
                padding-bottom: 2px;
                margin-top: 2px;
                background: transparent !important;
                opacity: 1 !important;
                cursor: pointer;
            }

            /* Hover background for separators */
            #adminmenu li a[href*="dab_separator_"]:hover {
                background-color: rgba(0, 0, 0, 0.2) !important;
            }

            /* Make sure the separator text is visible in both light and dark admin color schemes */
            .admin-color-light #adminmenu .dab-menu-separator,
            .admin-color-blue #adminmenu .dab-menu-separator,
            .admin-color-coffee #adminmenu .dab-menu-separator,
            .admin-color-ectoplasm #adminmenu .dab-menu-separator,
            .admin-color-midnight #adminmenu .dab-menu-separator,
            .admin-color-ocean #adminmenu .dab-menu-separator,
            .admin-color-sunrise #adminmenu .dab-menu-separator,
            .admin-color-fresh #adminmenu .dab-menu-separator {
                color: #f0f0f0 !important;
            }

            /* Add a subtle background to make headers stand out more */
            #adminmenu li a[href*="dab_separator_"] {
                background-color: rgba(0, 0, 0, 0.05) !important;
            }

            /* Hidden menu items */
            .dab-menu-group-hidden {
                display: none !important;
            }
        </style>';

        // Add JavaScript for collapsible menu functionality
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                // Define the menu groups and their items
                const menuGroups = {
                    "overview": ["dab_app_templates"],
                    "data-foundation": ["dab_tables", "dab_fields", "dab_relationships"],
                    "app-interface": ["dab_forms", "dab_views", "dab_dashboards", "dab_multistep_forms"],
                    "workflow-automation": ["dab_workflows", "dab_approval_config", "dab_approvals"],
                    "data-management": ["dab_data_dashboard"],
                    "user-management": ["dab_frontend_users", "dab_chat_groups", "dab_permissions_dashboard"],
                    "analytics": ["dab_report_builder", "dab_analytics_dashboard", "dab_data_insights", "dab_scheduled_reports"],
                    "woocommerce": ["dab_woocommerce_sales_dashboard", "dab_woocommerce_product_fields", "dab_woocommerce_order_fields", "dab_woocommerce_customer_data", "dab_woocommerce_inventory", "dab_woocommerce_advanced_checkout", "dab_woocommerce_marketing", "dab_woocommerce_enhanced_reports"],
                    "integrations": ["dab_payment_settings", "dab_payment_tracking", "dab_integrations"],
                    "configuration": ["dab_settings", "dab_license", "dab_uninstall_settings"],
                    "documentation": ["dab_documentation", "dab_permissions_docs"]
                };

                // Get saved collapsed state from localStorage
                const getCollapsedState = () => {
                    const savedState = localStorage.getItem("dab_collapsed_menu_groups");
                    return savedState ? JSON.parse(savedState) : {};
                };

                // Save collapsed state to localStorage
                const saveCollapsedState = (state) => {
                    localStorage.setItem("dab_collapsed_menu_groups", JSON.stringify(state));
                };

                // Initialize collapsed state with default (all collapsed)
                let collapsedState = getCollapsedState();

                // Set default state to collapsed for all groups if no saved state exists
                const isNewUser = Object.keys(collapsedState).length === 0;
                if (isNewUser) {
                    Object.keys(menuGroups).forEach(group => {
                        collapsedState[group] = true;
                    });
                    saveCollapsedState(collapsedState);
                }

                // Function to toggle a menu group
                const toggleMenuGroup = (groupName, forceState = null) => {
                    const isCollapsed = forceState !== null ? forceState : !collapsedState[groupName];
                    const separatorElement = document.querySelector(`.dab-menu-separator[data-group="${groupName}"]`);

                    if (!separatorElement) return;

                    // Update the collapsed state
                    collapsedState[groupName] = isCollapsed;
                    saveCollapsedState(collapsedState);

                    // Toggle the collapsed class on the separator
                    if (isCollapsed) {
                        separatorElement.classList.add("collapsed");
                    } else {
                        separatorElement.classList.remove("collapsed");
                    }

                    // Hide/show the menu items
                    menuGroups[groupName].forEach(itemSlug => {
                        const menuItem = document.querySelector(`#adminmenu a[href="admin.php?page=${itemSlug}"]`).closest("li");
                        if (menuItem) {
                            if (isCollapsed) {
                                menuItem.classList.add("dab-menu-group-hidden");
                            } else {
                                menuItem.classList.remove("dab-menu-group-hidden");
                            }
                        }
                    });
                };

                // Add click event listeners to all separators
                document.querySelectorAll(".dab-menu-separator").forEach(separator => {
                    const groupName = separator.getAttribute("data-group");

                    // Apply initial state - collapsed by default or based on saved state
                    toggleMenuGroup(groupName, collapsedState[groupName] !== false);

                    // Add click handler
                    const menuLink = separator.closest("a");
                    if (menuLink) {
                        menuLink.addEventListener("click", function(e) {
                            e.preventDefault();
                            toggleMenuGroup(groupName);
                        });
                    }
                });

                // Fix for WordPress admin menu - ensure the separators are clickable
                setTimeout(function() {
                    document.querySelectorAll(".dab-menu-separator").forEach(separator => {
                        const menuLink = separator.closest("a");
                        if (menuLink) {
                            menuLink.style.pointerEvents = "auto";
                            menuLink.style.cursor = "pointer";
                        }
                    });
                }, 500);
            });
        </script>';
    }

    public function load_dashboard_page() {
        require_once plugin_dir_path(__FILE__) . 'page-dashboard.php';
    }

    public function load_tables_page() {
        require_once plugin_dir_path(__FILE__) . 'page-tables.php';
    }

    public function load_fields_page() {
        // Check if we're editing a field
        if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['field_id'])) {
            require_once plugin_dir_path(__FILE__) . 'page-field-edit.php';
        } else {
            require_once plugin_dir_path(__FILE__) . 'page-fields.php';
        }
    }

    public function load_forms_page() {
        require_once plugin_dir_path(__FILE__) . 'page-forms.php';
    }

    public function load_relationships_page() {
        require_once plugin_dir_path(__FILE__) . 'page-relationships.php';
    }

    public function load_data_page() {
        require_once plugin_dir_path(__FILE__) . 'page-data.php';
    }

    public function load_views_page() {
        require_once plugin_dir_path(__FILE__) . 'page-views.php';
    }

    public function load_approval_config_page() {
        require_once plugin_dir_path(dirname(__FILE__)) . 'admin/page-approval-config.php';
    }

    public function load_approvals_page() {
        require_once plugin_dir_path(dirname(__FILE__)) . 'admin/page-approvals.php';
    }

    public function load_chat_groups_page() {
        require_once plugin_dir_path(__FILE__) . 'page-chat-groups.php';
    }

    public function load_settings_page() {
        require_once plugin_dir_path(__FILE__) . 'page-settings.php';
    }



    // Dashboard builder page loader functions
    public function load_dashboards_page() {
        require_once plugin_dir_path(__FILE__) . 'page-dashboards.php';
    }

    public function load_dashboard_builder_page() {
        require_once plugin_dir_path(__FILE__) . 'page-dashboard-builder.php';
    }

    public function load_dashboard_view_page() {
        require_once plugin_dir_path(__FILE__) . 'page-dashboard-view.php';
    }

    public function load_data_dashboard_page() {
        require_once plugin_dir_path(__FILE__) . 'page-data-dashboard.php';
    }

    public function load_workflows_page() {
        require_once plugin_dir_path(__FILE__) . 'page-workflows.php';
    }

    public function load_app_templates_page() {
        // Load the template manager page
        if (class_exists('DAB_Template_Manager')) {
            DAB_Template_Manager::render_templates_page();
        } else {
            echo '<div class="notice notice-error"><p>' . __('Template Manager not available.', 'db-app-builder') . '</p></div>';
        }
    }

    public function load_wizard_app_creation_page() {
        require_once plugin_dir_path(__FILE__) . 'wizard-app-creation.php';
    }

    public function load_wizard_workflow_setup_page() {
        require_once plugin_dir_path(__FILE__) . 'wizard-workflow-setup.php';
    }

    // Dashboard builder wizard page loader function has been removed

    public function load_payment_settings_page() {
        require_once plugin_dir_path(__FILE__) . 'page-payment-settings.php';
    }



    public function load_documentation_page() {
        // Check if the enhanced documentation exists
        $enhanced_docs = plugin_dir_path(dirname(__FILE__)) . 'documentation/frameset.html';

        if (file_exists($enhanced_docs)) {
            // If enhanced documentation exists, redirect to it
            $docs_url = plugin_dir_url(dirname(__FILE__)) . 'documentation/frameset.html';
            echo '<script>window.location.href = "' . esc_url($docs_url) . '";</script>';
            echo '<p>' . __('If you are not redirected automatically, please click ', 'db-app-builder') .
                 '<a href="' . esc_url($docs_url) . '">' . __('here', 'db-app-builder') . '</a>.</p>';
        } else {
            // Otherwise, load the basic documentation page
            require_once plugin_dir_path(__FILE__) . 'page-documentation.php';
        }
    }

    public function load_permissions_docs_page() {
        require_once plugin_dir_path(__FILE__) . 'page-permissions-docs.php';
    }

    public function load_permissions_dashboard_page() {
        require_once plugin_dir_path(__FILE__) . 'page-permissions-dashboard.php';
    }

    public function load_payment_tracking_page() {
        require_once plugin_dir_path(__FILE__) . 'page-payment-tracking.php';
    }

    public function load_integrations_page() {
        require_once plugin_dir_path(__FILE__) . 'page-integrations.php';
    }

    public function load_license_page() {
        require_once plugin_dir_path(__FILE__) . 'page-license.php';
    }

    public function load_uninstall_settings_page() {
        require_once plugin_dir_path(__FILE__) . 'page-uninstall-settings.php';
        dab_render_uninstall_settings_page();
    }

    // WooCommerce Integration page loaders
    public function load_woocommerce_product_fields_page() {
        require_once plugin_dir_path(__FILE__) . 'woocommerce/page-product-fields.php';
    }

    public function load_woocommerce_customer_data_page() {
        require_once plugin_dir_path(__FILE__) . 'woocommerce/page-customer-data.php';
    }

    public function load_woocommerce_order_fields_page() {
        require_once plugin_dir_path(__FILE__) . 'woocommerce/page-order-fields.php';
    }

    public function load_woocommerce_sales_dashboard_page() {
        require_once plugin_dir_path(__FILE__) . 'woocommerce/page-sales-dashboard.php';
    }

    // Phase 2 WooCommerce Integration page loaders
    public function load_woocommerce_inventory_page() {
        require_once plugin_dir_path(__FILE__) . 'woocommerce/page-inventory-management.php';
    }

    public function load_woocommerce_advanced_checkout_page() {
        require_once plugin_dir_path(__FILE__) . 'woocommerce/page-advanced-checkout.php';
    }

    public function load_woocommerce_marketing_page() {
        require_once plugin_dir_path(__FILE__) . 'woocommerce/page-marketing-automation.php';
    }

    public function load_woocommerce_enhanced_reports_page() {
        require_once plugin_dir_path(__FILE__) . 'woocommerce/page-enhanced-reports.php';
    }

    public function load_frontend_users_page() {
        require_once plugin_dir_path(__FILE__) . 'page-frontend-users.php';
    }

    // Phase 3: Analytics & Intelligence page loaders
    public function load_report_builder_page() {
        require_once plugin_dir_path(__FILE__) . 'analytics/page-report-builder.php';
    }

    public function load_analytics_dashboard_page() {
        require_once plugin_dir_path(__FILE__) . 'analytics/page-analytics-dashboard.php';
    }

    public function load_data_insights_page() {
        require_once plugin_dir_path(__FILE__) . 'analytics/page-data-insights.php';
    }

    public function load_scheduled_reports_page() {
        require_once plugin_dir_path(__FILE__) . 'analytics/page-scheduled-reports.php';
    }

    // Dashboard assignments page loader function has been removed
}

// Initialize Admin Menu
new DAB_Admin_Menu();
?>

