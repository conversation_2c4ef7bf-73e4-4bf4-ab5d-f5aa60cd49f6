<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Debug page to check data sources for analytics dashboard
global $wpdb;

// Check if dab_tables exists
$tables_table = $wpdb->prefix . 'dab_tables';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$tables_table'") === $tables_table;

echo '<div class="wrap">';
echo '<h1>Debug: Analytics Data Sources</h1>';

echo '<h2>Database Table Status</h2>';
echo '<p><strong>dab_tables table:</strong> ' . ($table_exists ? '✅ Exists' : '❌ Missing') . '</p>';

if ($table_exists) {
    // Get all tables
    $custom_tables = $wpdb->get_results("SELECT * FROM $tables_table ORDER BY table_label");
    
    echo '<h2>Custom Tables Found (' . count($custom_tables) . ')</h2>';
    
    if (empty($custom_tables)) {
        echo '<p>No custom tables found in the database.</p>';
        
        // Create a test table
        echo '<h3>Creating Test Table</h3>';
        
        $test_result = $wpdb->insert($tables_table, [
            'table_label' => 'Test Analytics Table',
            'table_slug' => 'test_analytics_table',
            'description' => 'Test table for analytics dashboard',
            'created_at' => current_time('mysql'),
        ]);
        
        if ($test_result) {
            $table_id = $wpdb->insert_id;
            echo '<p>✅ Test table created with ID: ' . $table_id . '</p>';
            
            // Create the data table
            DAB_DB_Manager::create_data_table('test_analytics_table');
            echo '<p>✅ Data table created</p>';
            
            // Create some test fields
            $fields_table = $wpdb->prefix . 'dab_fields';
            
            $wpdb->insert($fields_table, [
                'table_id' => $table_id,
                'field_label' => 'Name',
                'field_slug' => 'name',
                'field_type' => 'text',
                'required' => 1,
                'field_order' => 1,
                'created_at' => current_time('mysql')
            ]);
            
            $wpdb->insert($fields_table, [
                'table_id' => $table_id,
                'field_label' => 'Email',
                'field_slug' => 'email',
                'field_type' => 'email',
                'required' => 1,
                'field_order' => 2,
                'created_at' => current_time('mysql')
            ]);
            
            $wpdb->insert($fields_table, [
                'table_id' => $table_id,
                'field_label' => 'Score',
                'field_slug' => 'score',
                'field_type' => 'number',
                'required' => 0,
                'field_order' => 3,
                'created_at' => current_time('mysql')
            ]);
            
            echo '<p>✅ Test fields created</p>';
            
            // Add columns to data table
            $data_table = $wpdb->prefix . 'dab_test_analytics_table';
            DAB_DB_Manager::ensure_column_exists($data_table, 'name', 'VARCHAR(255)');
            DAB_DB_Manager::ensure_column_exists($data_table, 'email', 'VARCHAR(255)');
            DAB_DB_Manager::ensure_column_exists($data_table, 'score', 'INT');
            
            echo '<p>✅ Data table columns added</p>';
            
            // Insert some test data
            $wpdb->insert($data_table, [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'score' => 85,
                'created_at' => current_time('mysql')
            ]);
            
            $wpdb->insert($data_table, [
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'score' => 92,
                'created_at' => current_time('mysql')
            ]);
            
            $wpdb->insert($data_table, [
                'name' => 'Bob Johnson',
                'email' => '<EMAIL>',
                'score' => 78,
                'created_at' => current_time('mysql')
            ]);
            
            echo '<p>✅ Test data inserted</p>';
            
            // Refresh the tables list
            $custom_tables = $wpdb->get_results("SELECT * FROM $tables_table ORDER BY table_label");
        } else {
            echo '<p>❌ Failed to create test table</p>';
        }
    }
    
    if (!empty($custom_tables)) {
        echo '<table class="widefat">';
        echo '<thead><tr><th>ID</th><th>Label</th><th>Slug</th><th>Description</th><th>Created</th></tr></thead>';
        echo '<tbody>';
        
        foreach ($custom_tables as $table) {
            echo '<tr>';
            echo '<td>' . esc_html($table->id) . '</td>';
            echo '<td>' . esc_html($table->table_label) . '</td>';
            echo '<td>' . esc_html($table->table_slug) . '</td>';
            echo '<td>' . esc_html($table->description) . '</td>';
            echo '<td>' . esc_html($table->created_at) . '</td>';
            echo '</tr>';
        }
        
        echo '</tbody></table>';
    }
    
    // Test the AJAX endpoint
    echo '<h2>Test AJAX Endpoint</h2>';
    echo '<button id="test-ajax" class="button button-primary">Test Data Sources AJAX</button>';
    echo '<div id="ajax-result" style="margin-top: 10px; padding: 10px; background: #f9f9f9; border: 1px solid #ddd; display: none;"></div>';
    
    ?>
    <script>
    jQuery(document).ready(function($) {
        $('#test-ajax').click(function() {
            var $button = $(this);
            var $result = $('#ajax-result');
            
            $button.prop('disabled', true).text('Testing...');
            $result.show().html('Making AJAX request...');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'dab_get_analytics_data_sources',
                    nonce: '<?php echo wp_create_nonce('dab_admin_nonce'); ?>'
                },
                success: function(response) {
                    $result.html('<h4>AJAX Response:</h4><pre>' + JSON.stringify(response, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    $result.html('<h4>AJAX Error:</h4><p>Status: ' + status + '</p><p>Error: ' + error + '</p><p>Response: ' + xhr.responseText + '</p>');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Test Data Sources AJAX');
                }
            });
        });
    });
    </script>
    <?php
    
} else {
    echo '<p>❌ The dab_tables table does not exist. Please check your plugin installation.</p>';
}

echo '</div>';
?>
