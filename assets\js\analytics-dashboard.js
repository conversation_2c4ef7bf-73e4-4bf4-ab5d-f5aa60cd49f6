/**
 * Analytics Dashboard JavaScript
 * Phase 3: Data Intelligence & Analytics
 */

(function($) {
    'use strict';

    // Main Analytics Dashboard Object
    window.DAB_AnalyticsDashboard = {

        // Configuration
        config: {
            ajaxUrl: (typeof dab_admin_vars !== 'undefined' && dab_admin_vars.ajaxurl) ? dab_admin_vars.ajaxurl : ajaxurl,
            nonce: (typeof dab_admin_vars !== 'undefined' && dab_admin_vars.nonce) ? dab_admin_vars.nonce : '',
            refreshInterval: 30000, // 30 seconds
            autoRefresh: true
        },

        // State management
        state: {
            currentDashboard: null,
            widgets: {},
            refreshTimer: null,
            isFullscreen: false
        },

        // Initialize the dashboard
        init: function() {
            this.bindEvents();
            this.loadDashboards();
            this.initializeComponents();

            // Start auto-refresh if enabled
            if (this.config.autoRefresh) {
                this.startAutoRefresh();
            }
        },

        // Bind event handlers
        bindEvents: function() {
            var self = this;

            // Dashboard list events
            $(document).on('click', '.dab-dashboard-card', function() {
                var dashboardId = $(this).data('dashboard-id');
                self.viewDashboard(dashboardId);
            });

            // Dashboard builder events
            $(document).on('click', '#save-dashboard', function() {
                self.saveDashboard();
            });

            $(document).on('click', '#preview-dashboard', function() {
                self.previewDashboard();
            });

            $(document).on('click', '#export-dashboard', function() {
                self.exportDashboard();
            });

            // Widget events
            $(document).on('click', '.dab-widget-item', function() {
                self.addWidget($(this).data('widget-type'));
            });

            $(document).on('click', '.dab-widget-remove', function() {
                var widgetId = $(this).closest('.dab-widget').data('widget-id');
                self.removeWidget(widgetId);
            });

            $(document).on('click', '.dab-widget-edit', function() {
                var widgetId = $(this).closest('.dab-widget').data('widget-id');
                self.editWidget(widgetId);
            });

            // Refresh events
            $(document).on('click', '#refresh-dashboards', function() {
                self.loadDashboards();
            });

            $(document).on('click', '#refresh-data', function() {
                self.refreshDashboardData();
            });

            // Fullscreen events
            $(document).on('click', '#fullscreen-toggle, #fullscreen-view', function() {
                self.toggleFullscreen();
            });

            // Grid events
            $(document).on('click', '#grid-toggle', function() {
                self.toggleGrid();
            });

            // Share events
            $(document).on('click', '#share-dashboard', function() {
                self.shareDashboard();
            });

            // Filter events
            $(document).on('change', '#dashboard-filter, #search-dashboards', function() {
                self.filterDashboards();
            });
        },

        // Initialize components
        initializeComponents: function() {
            // Initialize drag and drop for widgets
            this.initializeDragDrop();

            // Initialize grid system
            this.initializeGrid();

            // Initialize real-time updates
            this.initializeRealTime();
        },

        // Load dashboards list
        loadDashboards: function() {
            var self = this;

            $('#dashboards-grid').html('<div class="dab-loading"><div class="spinner is-active"></div><p>Loading dashboards...</p></div>');

            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_get_dashboards_list',
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success) {
                        self.renderDashboardsList(response.data);
                        self.updateOverviewStats(response.data.stats);
                    } else {
                        self.showError('Failed to load dashboards: ' + response.data);
                    }
                },
                error: function() {
                    self.showError('Network error while loading dashboards');
                }
            });
        },

        // Render dashboards list
        renderDashboardsList: function(data) {
            var html = '';

            if (data.dashboards && data.dashboards.length > 0) {
                data.dashboards.forEach(function(dashboard) {
                    html += '<div class="dab-dashboard-card" data-dashboard-id="' + dashboard.id + '">';
                    html += '<div class="dab-card-header">';
                    html += '<h3>' + dashboard.name + '</h3>';
                    html += '<div class="dab-card-status ' + (dashboard.is_public ? 'public' : 'private') + '">';
                    html += dashboard.is_public ? 'Public' : 'Private';
                    html += '</div>';
                    html += '</div>';
                    html += '<div class="dab-card-content">';
                    html += '<p>' + (dashboard.description || 'No description') + '</p>';
                    html += '<div class="dab-card-meta">';
                    html += '<span><i class="dashicons dashicons-chart-line"></i> ' + dashboard.widget_count + ' widgets</span>';
                    html += '<span><i class="dashicons dashicons-visibility"></i> ' + dashboard.access_count + ' views</span>';
                    html += '</div>';
                    html += '</div>';
                    html += '<div class="dab-card-actions">';
                    html += '<button class="button" onclick="DAB_AnalyticsDashboard.viewDashboard(' + dashboard.id + ')">View</button>';
                    html += '<button class="button" onclick="DAB_AnalyticsDashboard.editDashboard(' + dashboard.id + ')">Edit</button>';
                    html += '<button class="button" onclick="DAB_AnalyticsDashboard.duplicateDashboard(' + dashboard.id + ')">Duplicate</button>';
                    html += '</div>';
                    html += '</div>';
                });
            } else {
                html = '<div class="dab-empty-state">';
                html += '<div class="dab-empty-icon"><span class="dashicons dashicons-dashboard"></span></div>';
                html += '<h3>No dashboards found</h3>';
                html += '<p>Create your first analytics dashboard to get started.</p>';
                html += '<a href="' + window.location.href + '&action=create" class="button button-primary">Create Dashboard</a>';
                html += '</div>';
            }

            $('#dashboards-grid').html(html);
        },

        // Update overview statistics
        updateOverviewStats: function(stats) {
            $('#total-dashboards').text(stats.total_dashboards || 0);
            $('#active-widgets').text(stats.active_widgets || 0);
            $('#total-views').text(stats.total_views || 0);
            $('#realtime-updates').text(stats.realtime_updates || 0);
        },

        // View dashboard
        viewDashboard: function(dashboardId) {
            window.location.href = window.location.href.split('&')[0] + '&action=view&dashboard_id=' + dashboardId;
        },

        // Edit dashboard
        editDashboard: function(dashboardId) {
            window.location.href = window.location.href.split('&')[0] + '&action=edit&dashboard_id=' + dashboardId;
        },

        // Duplicate dashboard
        duplicateDashboard: function(dashboardId) {
            var self = this;

            if (!confirm('Are you sure you want to duplicate this dashboard?')) {
                return;
            }

            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_duplicate_dashboard',
                    dashboard_id: dashboardId,
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success) {
                        self.showSuccess('Dashboard duplicated successfully');
                        self.loadDashboards();
                    } else {
                        self.showError('Failed to duplicate dashboard: ' + response.data);
                    }
                },
                error: function() {
                    self.showError('Network error while duplicating dashboard');
                }
            });
        },

        // Save dashboard
        saveDashboard: function() {
            var self = this;
            var dashboardData = this.collectDashboardData();

            if (!this.validateDashboardData(dashboardData)) {
                return;
            }

            // Show loading state
            var $saveBtn = $('#save-dashboard');
            var originalText = $saveBtn.html();
            $saveBtn.html('<span class="dashicons dashicons-update" style="animation: spin 1s linear infinite;"></span> Saving...').prop('disabled', true);

            // For now, simulate saving since we don't have the backend endpoint
            setTimeout(function() {
                try {
                    // Validate dashboard data
                    if (!dashboardData.name || dashboardData.name.trim() === '') {
                        throw new Error('Dashboard name is required');
                    }

                    // Simulate successful save
                    self.showSuccess('Dashboard saved successfully');
                    self.state.currentDashboard = 'dashboard_' + Date.now();
                    $('#canvas-status').text('Saved').removeClass('draft').addClass('saved');

                    // Store dashboard data in localStorage for demo purposes
                    var savedDashboards = JSON.parse(localStorage.getItem('dab_saved_dashboards') || '[]');
                    var dashboardToSave = {
                        id: self.state.currentDashboard,
                        name: dashboardData.name,
                        description: dashboardData.description,
                        widgets: self.state.widgets,
                        settings: dashboardData,
                        saved_at: new Date().toISOString()
                    };

                    // Remove existing dashboard with same name
                    savedDashboards = savedDashboards.filter(function(d) { return d.name !== dashboardData.name; });
                    savedDashboards.push(dashboardToSave);
                    localStorage.setItem('dab_saved_dashboards', JSON.stringify(savedDashboards));

                } catch (error) {
                    self.showError('Failed to save dashboard: ' + error.message);
                } finally {
                    // Always restore button state
                    $saveBtn.html(originalText).prop('disabled', false);
                }

                // In a real implementation, you would make an AJAX call like this:
                /*
                $.ajax({
                    url: self.config.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'dab_save_analytics_dashboard',
                        dashboard_data: JSON.stringify(dashboardData),
                        nonce: self.config.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            self.showSuccess('Dashboard saved successfully');
                            self.state.currentDashboard = response.data.dashboard_id;
                            $('#canvas-status').text('Saved').removeClass('draft').addClass('saved');
                        } else {
                            self.showError('Failed to save dashboard: ' + response.data);
                        }
                    },
                    error: function() {
                        self.showError('Network error while saving dashboard');
                    },
                    complete: function() {
                        $saveBtn.html(originalText).prop('disabled', false);
                    }
                });
                */
            }, 1500);
        },

        // Collect dashboard data from form
        collectDashboardData: function() {
            return {
                name: $('#dashboard-name').val(),
                description: $('#dashboard-description').val(),
                refresh_interval: $('#refresh-interval').val(),
                auto_refresh: $('#auto-refresh').is(':checked') ? 1 : 0,
                is_public: $('#is-public').is(':checked') ? 1 : 0,
                layout_config: this.getLayoutConfig(),
                theme_config: this.getThemeConfig(),
                widgets: this.getWidgetsConfig()
            };
        },

        // Validate dashboard data
        validateDashboardData: function(data) {
            if (!data.name || data.name.trim() === '') {
                this.showError('Dashboard name is required');
                $('#dashboard-name').focus();
                return false;
            }

            return true;
        },

        // Get layout configuration
        getLayoutConfig: function() {
            // Collect layout configuration from the grid
            var layout = [];
            $('.dab-widget').each(function() {
                var $widget = $(this);
                layout.push({
                    widget_id: $widget.data('widget-id'),
                    x: parseInt($widget.css('left')) || 0,
                    y: parseInt($widget.css('top')) || 0,
                    width: parseInt($widget.css('width')) || 200,
                    height: parseInt($widget.css('height')) || 150
                });
            });
            return layout;
        },

        // Get theme configuration
        getThemeConfig: function() {
            return {
                background_color: '#ffffff',
                text_color: '#333333',
                accent_color: '#667eea',
                grid_size: 20
            };
        },

        // Get widgets configuration
        getWidgetsConfig: function() {
            var widgets = [];
            $('.dab-widget').each(function() {
                var $widget = $(this);
                var widgetId = $widget.data('widget-id');
                if (this.state.widgets[widgetId]) {
                    widgets.push(this.state.widgets[widgetId]);
                }
            }.bind(this));
            return widgets;
        },

        // Initialize drag and drop
        initializeDragDrop: function() {
            var self = this;

            // Make widgets draggable from library
            $('.dab-widget-item').draggable({
                helper: 'clone',
                revert: 'invalid',
                zIndex: 1000,
                cursor: 'move',
                opacity: 0.8,
                start: function(event, ui) {
                    $(this).addClass('ui-draggable-dragging');
                    $('#dashboard-grid').addClass('ui-droppable-hover');
                },
                stop: function(event, ui) {
                    $(this).removeClass('ui-draggable-dragging');
                    $('#dashboard-grid').removeClass('ui-droppable-hover');
                }
            });

            // Make dashboard grid droppable
            $('#dashboard-grid').droppable({
                accept: '.dab-widget-item',
                tolerance: 'pointer',
                hoverClass: 'ui-droppable-hover',
                drop: function(event, ui) {
                    var widgetType = ui.draggable.data('widget-type');
                    var position = {
                        left: event.pageX - $(this).offset().left,
                        top: event.pageY - $(this).offset().top
                    };
                    self.addWidget(widgetType, position);
                    $(this).removeClass('ui-droppable-hover');
                }
            });

            // Make existing widgets draggable
            $(document).on('mousedown', '.dab-widget', function() {
                var $widget = $(this);
                if (!$widget.hasClass('ui-draggable')) {
                    $widget.draggable({
                        handle: '.dab-widget-header',
                        containment: '#dashboard-grid',
                        grid: [20, 20],
                        cursor: 'move',
                        opacity: 0.8,
                        start: function() {
                            $(this).addClass('ui-draggable-dragging');
                        },
                        stop: function() {
                            $(this).removeClass('ui-draggable-dragging');
                            self.updateWidgetPosition($(this));
                        }
                    });
                }
            });
        },

        // Initialize grid system
        initializeGrid: function() {
            this.updateGridBackground();
        },

        // Update grid background
        updateGridBackground: function() {
            var gridSize = 20;
            var $grid = $('.dab-grid-background');
            $grid.css({
                'background-size': gridSize + 'px ' + gridSize + 'px'
            });
        },

        // Toggle grid visibility
        toggleGrid: function() {
            var $gridBackground = $('.dab-grid-background');
            var $toggleBtn = $('#grid-toggle');

            if ($gridBackground.is(':visible')) {
                // Hide grid
                $gridBackground.fadeOut(200);
                $toggleBtn.removeClass('active').find('.dashicons').removeClass('dashicons-grid-view').addClass('dashicons-visibility');
                $toggleBtn.attr('title', 'Show Grid');
                this.showSuccess('Grid hidden');
            } else {
                // Show grid
                $gridBackground.fadeIn(200);
                $toggleBtn.addClass('active').find('.dashicons').removeClass('dashicons-visibility').addClass('dashicons-grid-view');
                $toggleBtn.attr('title', 'Hide Grid');
                this.showSuccess('Grid visible');
            }
        },

        // Initialize real-time updates
        initializeRealTime: function() {
            // Set up WebSocket or polling for real-time updates
            this.setupRealTimePolling();
        },

        // Set up real-time polling
        setupRealTimePolling: function() {
            var self = this;

            if (this.state.refreshTimer) {
                clearInterval(this.state.refreshTimer);
            }

            this.state.refreshTimer = setInterval(function() {
                if (self.config.autoRefresh && self.state.currentDashboard) {
                    self.refreshDashboardData();
                }
            }, this.config.refreshInterval);
        },

        // Start auto-refresh
        startAutoRefresh: function() {
            this.config.autoRefresh = true;
            this.setupRealTimePolling();
        },

        // Stop auto-refresh
        stopAutoRefresh: function() {
            this.config.autoRefresh = false;
            if (this.state.refreshTimer) {
                clearInterval(this.state.refreshTimer);
                this.state.refreshTimer = null;
            }
        },

        // Refresh dashboard data
        refreshDashboardData: function() {
            var self = this;

            if (!this.state.currentDashboard) {
                return;
            }

            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_get_realtime_data',
                    dashboard_id: this.state.currentDashboard,
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success) {
                        self.updateWidgetsData(response.data);
                        self.updateLastRefreshTime();
                    }
                },
                error: function() {
                    console.warn('Failed to refresh dashboard data');
                }
            });
        },

        // Update widgets with new data
        updateWidgetsData: function(data) {
            for (var widgetId in data) {
                if (data.hasOwnProperty(widgetId)) {
                    this.updateWidgetData(widgetId, data[widgetId]);
                }
            }
        },

        // Update individual widget data
        updateWidgetData: function(widgetId, data) {
            var $widget = $('.dab-widget[data-widget-id="' + widgetId + '"]');
            if ($widget.length > 0) {
                // Update widget content based on type
                var widgetType = $widget.data('widget-type');
                this.renderWidgetData($widget, widgetType, data);
            }
        },

        // Render widget data based on type
        renderWidgetData: function($widget, type, data) {
            var $content = $widget.find('.dab-widget-content');

            switch (type) {
                case 'metric':
                    this.renderMetricWidget($content, data);
                    break;
                case 'chart':
                    this.renderChartWidget($content, data);
                    break;
                case 'table':
                    this.renderTableWidget($content, data);
                    break;
                case 'gauge':
                    this.renderGaugeWidget($content, data);
                    break;
                default:
                    $content.html('<p>Unknown widget type: ' + type + '</p>');
            }
        },

        // Render metric widget with proper configuration
        renderMetricWidget: function($content, data) {
            var widget = this.getWidgetFromContent($content);
            var config = widget ? this.state.widgets[widget.id] : {};

            var html = '<div class="dab-metric-widget">';
            html += '<div class="dab-metric-header">';
            html += '<span class="dab-metric-icon dashicons dashicons-chart-area"></span>';
            html += '<span class="dab-metric-title">' + (config.title || 'Metric') + '</span>';
            html += '</div>';

            var value = this.calculateMetricValue(data, config);
            var formattedValue = this.formatMetricValue(value, config.aggregation);

            html += '<div class="dab-metric-value">' + formattedValue + '</div>';
            html += '<div class="dab-metric-label">' + this.getMetricLabel(config) + '</div>';

            if (data.previousValue !== undefined) {
                var change = this.calculateChange(value, data.previousValue);
                var changeClass = change > 0 ? 'positive' : (change < 0 ? 'negative' : 'neutral');
                var changeIcon = change > 0 ? 'arrow-up-alt' : (change < 0 ? 'arrow-down-alt' : 'minus');
                html += '<div class="dab-metric-change ' + changeClass + '">';
                html += '<span class="dashicons dashicons-' + changeIcon + '"></span>';
                html += Math.abs(change).toFixed(1) + '%';
                html += '</div>';
            }

            html += '<div class="dab-metric-footer">';
            html += '<span class="dab-metric-source">Source: ' + this.getDataSourceName(config.dataSource) + '</span>';
            html += '<span class="dab-metric-updated">Updated: ' + new Date().toLocaleTimeString() + '</span>';
            html += '</div>';
            html += '</div>';

            $content.html(html);
        },

        // Render chart widget with proper visualization
        renderChartWidget: function($content, data) {
            var widget = this.getWidgetFromContent($content);
            var config = widget ? this.state.widgets[widget.id] : {};

            var chartId = 'chart-' + (widget ? widget.id : Date.now());
            var html = '<div class="dab-chart-widget">';
            html += '<div class="dab-chart-header">';
            html += '<span class="dab-chart-title">' + (config.title || 'Chart') + '</span>';
            html += '<div class="dab-chart-controls">';
            html += '<button class="dab-chart-refresh" title="Refresh"><span class="dashicons dashicons-update"></span></button>';
            html += '<button class="dab-chart-fullscreen" title="Fullscreen"><span class="dashicons dashicons-fullscreen-alt"></span></button>';
            html += '</div>';
            html += '</div>';
            html += '<div class="dab-chart-container">';
            html += '<canvas id="' + chartId + '" width="400" height="200"></canvas>';
            html += '</div>';
            html += '<div class="dab-chart-footer">';
            html += '<span class="dab-chart-type">' + (config.chartType || 'bar').toUpperCase() + ' Chart</span>';
            html += '<span class="dab-chart-records">' + (data.length || 0) + ' records</span>';
            html += '</div>';
            html += '</div>';

            $content.html(html);

            // Render actual chart
            setTimeout(() => {
                this.renderChart(chartId, data, config);
            }, 100);
        },

        // Render table widget with proper data display
        renderTableWidget: function($content, data) {
            var widget = this.getWidgetFromContent($content);
            var config = widget ? this.state.widgets[widget.id] : {};

            var html = '<div class="dab-table-widget">';
            html += '<div class="dab-table-header">';
            html += '<span class="dab-table-title">' + (config.title || 'Data Table') + '</span>';
            html += '<div class="dab-table-controls">';
            html += '<input type="text" class="dab-table-search" placeholder="Search...">';
            html += '<button class="dab-table-export" title="Export"><span class="dashicons dashicons-download"></span></button>';
            html += '</div>';
            html += '</div>';

            if (data && data.length > 0) {
                html += '<div class="dab-table-container">';
                html += this.generateTableHTML(data, config);
                html += '</div>';
                html += '<div class="dab-table-footer">';
                html += '<span class="dab-table-count">Showing ' + Math.min(data.length, config.recordLimit || 10) + ' of ' + data.length + ' records</span>';
                html += '<div class="dab-table-pagination">';
                html += '<button class="dab-table-prev" disabled><span class="dashicons dashicons-arrow-left-alt2"></span></button>';
                html += '<span class="dab-table-page">Page 1 of 1</span>';
                html += '<button class="dab-table-next" disabled><span class="dashicons dashicons-arrow-right-alt2"></span></button>';
                html += '</div>';
                html += '</div>';
            } else {
                html += '<div class="dab-table-empty">No data available</div>';
            }

            html += '</div>';
            $content.html(html);
        },

        // Render gauge widget with proper visualization
        renderGaugeWidget: function($content, data) {
            var widget = this.getWidgetFromContent($content);
            var config = widget ? this.state.widgets[widget.id] : {};

            var gaugeId = 'gauge-' + (widget ? widget.id : Date.now());
            var value = this.calculateGaugeValue(data, config);
            var maxValue = config.maxValue || 100;
            var percentage = Math.min((value / maxValue) * 100, 100);

            var html = '<div class="dab-gauge-widget">';
            html += '<div class="dab-gauge-header">';
            html += '<span class="dab-gauge-title">' + (config.title || 'Gauge') + '</span>';
            html += '</div>';
            html += '<div class="dab-gauge-container" id="' + gaugeId + '">';
            html += '<svg class="dab-gauge-svg" viewBox="0 0 200 120">';
            html += '<defs>';
            html += '<linearGradient id="gaugeGradient" x1="0%" y1="0%" x2="100%" y2="0%">';
            html += '<stop offset="0%" style="stop-color:#ff4757;stop-opacity:1" />';
            html += '<stop offset="50%" style="stop-color:#ffa502;stop-opacity:1" />';
            html += '<stop offset="100%" style="stop-color:#2ed573;stop-opacity:1" />';
            html += '</linearGradient>';
            html += '</defs>';
            html += '<path d="M 20 100 A 80 80 0 0 1 180 100" stroke="#e1e1e1" stroke-width="8" fill="none"/>';
            html += '<path d="M 20 100 A 80 80 0 0 1 ' + this.calculateGaugeArcEnd(percentage) + '" stroke="url(#gaugeGradient)" stroke-width="8" fill="none" stroke-linecap="round"/>';
            html += '<circle cx="100" cy="100" r="4" fill="#333"/>';
            html += '<text x="100" y="90" text-anchor="middle" class="dab-gauge-value">' + value + '</text>';
            html += '<text x="100" y="110" text-anchor="middle" class="dab-gauge-label">' + percentage.toFixed(1) + '%</text>';
            html += '</svg>';
            html += '</div>';
            html += '<div class="dab-gauge-footer">';
            html += '<span class="dab-gauge-range">0 - ' + maxValue + '</span>';
            html += '<span class="dab-gauge-field">' + (config.valueField || 'Value') + '</span>';
            html += '</div>';
            html += '</div>';

            $content.html(html);
        },

        // Add widget to dashboard
        addWidget: function(widgetType, position) {
            var self = this;

            // Generate unique widget ID
            var widgetId = 'widget_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            // Calculate position relative to grid
            var gridOffset = $('#dashboard-grid').offset();
            var relativeX = (position ? position.left : 100) - (gridOffset ? gridOffset.left : 0);
            var relativeY = (position ? position.top : 100) - (gridOffset ? gridOffset.top : 0);

            // Snap to grid
            var gridSize = 20;
            relativeX = Math.round(relativeX / gridSize) * gridSize;
            relativeY = Math.round(relativeY / gridSize) * gridSize;

            // Create widget HTML
            var widgetHtml = this.createWidgetHTML(widgetId, widgetType, relativeX, relativeY);

            // Add widget to grid
            $('#dashboard-grid').append(widgetHtml);

            // Hide drop zone message if this is the first widget
            $('.dab-drop-zone').hide();

            // Store widget in state
            this.state.widgets[widgetId] = {
                id: widgetId,
                type: widgetType,
                x: relativeX,
                y: relativeY,
                width: 200,
                height: 150,
                title: this.getWidgetTitle(widgetType),
                dataSource: '', // No data source selected by default
                refreshInterval: 0 // Manual refresh by default
            };

            // Initialize widget functionality
            this.initializeWidget(widgetId);

            // Mark dashboard as modified
            $('#canvas-status').text('Modified').removeClass('saved').addClass('draft');

            console.log('Widget added:', widgetId, widgetType);
        },

        // Create widget HTML
        createWidgetHTML: function(widgetId, widgetType, x, y) {
            var title = this.getWidgetTitle(widgetType);
            var iconClass = this.getWidgetIcon(widgetType);

            var html = '<div class="dab-widget" data-widget-id="' + widgetId + '" data-widget-type="' + widgetType + '" ';
            html += 'style="position: absolute; left: ' + x + 'px; top: ' + y + 'px; width: 200px; height: 150px; ';
            html += 'background: white; border: 1px solid #ddd; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">';

            // Widget header
            html += '<div class="dab-widget-header" style="display: flex; justify-content: space-between; align-items: center; ';
            html += 'padding: 10px; border-bottom: 1px solid #eee; background: #f8f9fa; border-radius: 6px 6px 0 0; cursor: move;">';
            html += '<h3 class="dab-widget-title" style="margin: 0; font-size: 14px; color: #333;">';
            html += '<span class="dashicons ' + iconClass + '" style="margin-right: 5px;"></span>' + title;
            html += '</h3>';
            html += '<div class="dab-widget-actions">';
            html += '<button type="button" class="dab-widget-edit" title="Edit Widget" style="background: none; border: none; cursor: pointer; padding: 2px;">';
            html += '<span class="dashicons dashicons-edit" style="font-size: 16px; color: #666;"></span>';
            html += '</button>';
            html += '<button type="button" class="dab-widget-remove" title="Remove Widget" style="background: none; border: none; cursor: pointer; padding: 2px; margin-left: 5px;">';
            html += '<span class="dashicons dashicons-no-alt" style="font-size: 16px; color: #d63638;"></span>';
            html += '</button>';
            html += '</div>';
            html += '</div>';

            // Widget content
            html += '<div class="dab-widget-content" style="padding: 15px; height: calc(100% - 50px); overflow: auto;">';
            html += '<div class="dab-widget-loading" style="text-align: center; color: #666;">';
            html += '<span class="dashicons dashicons-update" style="animation: spin 1s linear infinite;"></span>';
            html += '<p style="margin: 10px 0 0 0;">Loading ' + title.toLowerCase() + '...</p>';
            html += '</div>';
            html += '</div>';

            html += '</div>';

            return html;
        },

        // Get widget title by type
        getWidgetTitle: function(type) {
            var titles = {
                'metric': 'Metric Card',
                'chart': 'Chart Widget',
                'table': 'Data Table',
                'gauge': 'Gauge Chart',
                'map': 'Map Widget',
                'text': 'Text Widget'
            };
            return titles[type] || 'Unknown Widget';
        },

        // Get widget icon by type
        getWidgetIcon: function(type) {
            var icons = {
                'metric': 'dashicons-chart-area',
                'chart': 'dashicons-chart-bar',
                'table': 'dashicons-list-view',
                'gauge': 'dashicons-performance',
                'map': 'dashicons-location',
                'text': 'dashicons-text'
            };
            return icons[type] || 'dashicons-admin-generic';
        },

        // Initialize widget functionality
        initializeWidget: function(widgetId) {
            var self = this;
            var $widget = $('.dab-widget[data-widget-id="' + widgetId + '"]');

            // Make widget draggable
            $widget.draggable({
                handle: '.dab-widget-header',
                containment: '#dashboard-grid',
                grid: [20, 20],
                stop: function() {
                    self.updateWidgetPosition($widget);
                }
            });

            // Make widget resizable
            $widget.resizable({
                handles: 'se',
                minWidth: 150,
                minHeight: 100,
                grid: [20, 20],
                stop: function() {
                    self.updateWidgetSize($widget);
                }
            });

            // Load widget content
            setTimeout(function() {
                self.loadWidgetContent(widgetId);
            }, 500);
        },

        // Update widget position
        updateWidgetPosition: function($widget) {
            var widgetId = $widget.data('widget-id');
            var position = $widget.position();

            if (this.state.widgets[widgetId]) {
                this.state.widgets[widgetId].x = position.left;
                this.state.widgets[widgetId].y = position.top;

                // Mark dashboard as modified
                $('#canvas-status').text('Modified').removeClass('saved').addClass('draft');
            }
        },

        // Update widget size
        updateWidgetSize: function($widget) {
            var widgetId = $widget.data('widget-id');
            var width = $widget.width();
            var height = $widget.height();

            if (this.state.widgets[widgetId]) {
                this.state.widgets[widgetId].width = width;
                this.state.widgets[widgetId].height = height;

                // Mark dashboard as modified
                $('#canvas-status').text('Modified').removeClass('saved').addClass('draft');
            }
        },

        // Load widget content
        loadWidgetContent: function(widgetId) {
            var self = this;
            var $widget = $('.dab-widget[data-widget-id="' + widgetId + '"]');
            var widgetType = $widget.data('widget-type');
            var $content = $widget.find('.dab-widget-content');
            var widget = this.state.widgets[widgetId];

            // Simulate loading delay
            setTimeout(function() {
                var hasDataSource = widget && widget.dataSource && widget.dataSource !== '';
                var dataSourceName = hasDataSource ? self.getDataSourceName(widget.dataSource) : 'No data source';

                if (!hasDataSource) {
                    // Show configuration needed message
                    $content.html('<div style="text-align: center; padding: 20px; color: #666;">' +
                        '<div style="margin-bottom: 10px;"><span class="dashicons dashicons-admin-settings" style="font-size: 24px;"></span></div>' +
                        '<p style="margin: 0 0 10px 0;">Configuration needed</p>' +
                        '<p style="margin: 0; font-size: 12px;">Click edit to configure data source</p>' +
                        '</div>');
                    return;
                }

                // Render widget based on type with data source info
                switch (widgetType) {
                    case 'metric':
                        $content.html('<div class="dab-metric-widget" style="text-align: center;">' +
                            '<div class="dab-metric-value" style="font-size: 2em; font-weight: bold; color: #667eea;">42</div>' +
                            '<div class="dab-metric-label" style="color: #666; margin-top: 5px;">Sample Metric</div>' +
                            '<div style="font-size: 11px; color: #999; margin-top: 5px;">Source: ' + dataSourceName + '</div>' +
                            '</div>');
                        break;
                    case 'chart':
                        $content.html('<div class="dab-chart-widget" style="text-align: center; padding: 15px;">' +
                            '<div style="background: #f0f0f0; height: 60px; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #666; margin-bottom: 8px;">' +
                            '<span class="dashicons dashicons-chart-bar" style="font-size: 20px; margin-right: 8px;"></span>Chart Preview' +
                            '</div>' +
                            '<div style="font-size: 11px; color: #999;">Source: ' + dataSourceName + '</div>' +
                            '</div>');
                        break;
                    case 'table':
                        $content.html('<div class="dab-table-widget">' +
                            '<table style="width: 100%; border-collapse: collapse; font-size: 12px;">' +
                            '<thead><tr style="background: #f8f9fa;"><th style="padding: 6px; border: 1px solid #ddd;">Column 1</th><th style="padding: 6px; border: 1px solid #ddd;">Column 2</th></tr></thead>' +
                            '<tbody><tr><td style="padding: 6px; border: 1px solid #ddd;">Data 1</td><td style="padding: 6px; border: 1px solid #ddd;">Data 2</td></tr></tbody>' +
                            '</table>' +
                            '<div style="font-size: 11px; color: #999; margin-top: 5px; text-align: center;">Source: ' + dataSourceName + '</div>' +
                            '</div>');
                        break;
                    case 'gauge':
                        $content.html('<div class="dab-gauge-widget" style="text-align: center; padding: 15px;">' +
                            '<div style="width: 60px; height: 60px; border: 6px solid #667eea; border-radius: 50%; margin: 0 auto 8px auto; display: flex; align-items: center; justify-content: center; color: #667eea; font-weight: bold; font-size: 14px;">75%</div>' +
                            '<div style="font-size: 11px; color: #999;">Source: ' + dataSourceName + '</div>' +
                            '</div>');
                        break;
                    case 'map':
                        $content.html('<div class="dab-map-widget" style="text-align: center; padding: 15px;">' +
                            '<div style="background: #e8f4f8; height: 60px; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #666; margin-bottom: 8px;">' +
                            '<span class="dashicons dashicons-location" style="font-size: 20px; margin-right: 8px;"></span>Map Preview' +
                            '</div>' +
                            '<div style="font-size: 11px; color: #999;">Source: ' + dataSourceName + '</div>' +
                            '</div>');
                        break;
                    case 'text':
                        $content.html('<div class="dab-text-widget">' +
                            '<p style="margin: 0 0 8px 0; color: #333; font-size: 13px;">This is a sample text widget. You can add custom content here.</p>' +
                            '<div style="font-size: 11px; color: #999;">Source: ' + dataSourceName + '</div>' +
                            '</div>');
                        break;
                    default:
                        $content.html('<div style="text-align: center; color: #666; padding: 20px;">Unknown widget type: ' + widgetType + '</div>');
                }
            }, 300);
        },

        // Get data source name by ID
        getDataSourceName: function(dataSourceId) {
            // Try to get from cached data sources first
            if (this.cachedDataSources) {
                var source = this.cachedDataSources.find(function(s) { return s.id === dataSourceId; });
                if (source) {
                    return source.name + (source.type ? ' (' + source.type + ')' : '');
                }
            }

            // Fallback to static mapping
            var dataSources = {
                'wp_users': 'WordPress Users',
                'wp_posts': 'WordPress Posts',
                'wp_comments': 'WordPress Comments',
                'sample_table_1': 'Sample Table 1',
                'sample_table_2': 'Sample Table 2'
            };
            return dataSources[dataSourceId] || dataSourceId;
        },

        // Get widget from content element
        getWidgetFromContent: function($content) {
            var $widget = $content.closest('.dab-widget');
            if ($widget.length > 0) {
                return {
                    id: $widget.data('widget-id'),
                    type: $widget.data('widget-type')
                };
            }
            return null;
        },

        // Calculate metric value based on configuration
        calculateMetricValue: function(data, config) {
            if (!data || !config.dataSource) return 0;

            var aggregation = config.aggregation || 'count';
            var field = config.metricField || 'id';

            switch (aggregation) {
                case 'count':
                    return Array.isArray(data) ? data.length : (data.count || 0);
                case 'sum':
                    return Array.isArray(data) ? data.reduce((sum, item) => sum + (parseFloat(item[field]) || 0), 0) : (data.sum || 0);
                case 'avg':
                    if (Array.isArray(data) && data.length > 0) {
                        var sum = data.reduce((sum, item) => sum + (parseFloat(item[field]) || 0), 0);
                        return sum / data.length;
                    }
                    return data.avg || 0;
                case 'max':
                    return Array.isArray(data) ? Math.max(...data.map(item => parseFloat(item[field]) || 0)) : (data.max || 0);
                case 'min':
                    return Array.isArray(data) ? Math.min(...data.map(item => parseFloat(item[field]) || 0)) : (data.min || 0);
                default:
                    return 0;
            }
        },

        // Format metric value for display
        formatMetricValue: function(value, aggregation) {
            if (typeof value !== 'number') return '0';

            if (aggregation === 'avg') {
                return value.toFixed(2);
            } else if (value >= 1000000) {
                return (value / 1000000).toFixed(1) + 'M';
            } else if (value >= 1000) {
                return (value / 1000).toFixed(1) + 'K';
            }
            return Math.round(value).toString();
        },

        // Get metric label based on configuration
        getMetricLabel: function(config) {
            var aggregation = config.aggregation || 'count';
            var field = config.metricField || 'records';

            var labels = {
                'count': 'Total ' + field,
                'sum': 'Sum of ' + field,
                'avg': 'Average ' + field,
                'max': 'Maximum ' + field,
                'min': 'Minimum ' + field
            };

            return labels[aggregation] || 'Metric';
        },

        // Calculate percentage change
        calculateChange: function(current, previous) {
            if (!previous || previous === 0) return 0;
            return ((current - previous) / previous) * 100;
        },

        // Calculate gauge value
        calculateGaugeValue: function(data, config) {
            if (!data || !config.valueField) return 0;

            if (Array.isArray(data)) {
                return data.length; // Default to count
            }

            return parseFloat(data[config.valueField]) || 0;
        },

        // Calculate gauge arc end point
        calculateGaugeArcEnd: function(percentage) {
            var angle = (percentage / 100) * Math.PI; // Convert to radians
            var x = 100 + 80 * Math.cos(Math.PI - angle);
            var y = 100 - 80 * Math.sin(Math.PI - angle);
            return x + ' ' + y;
        },

        // Generate table HTML
        generateTableHTML: function(data, config) {
            if (!data || data.length === 0) {
                return '<div class="dab-table-empty">No data available</div>';
            }

            var selectedFields = config.selectedFields || Object.keys(data[0]);
            var limit = config.recordLimit || 10;
            var sortField = config.sortField || 'id';

            // Sort data
            var sortedData = [...data].sort((a, b) => {
                var aVal = a[sortField];
                var bVal = b[sortField];
                if (typeof aVal === 'string') {
                    return bVal.localeCompare(aVal); // Descending for strings
                }
                return bVal - aVal; // Descending for numbers
            });

            // Limit data
            var limitedData = sortedData.slice(0, limit);

            var html = '<table class="dab-data-table">';
            html += '<thead><tr>';
            selectedFields.forEach(field => {
                html += '<th>' + this.formatFieldName(field) + '</th>';
            });
            html += '</tr></thead>';
            html += '<tbody>';

            limitedData.forEach(row => {
                html += '<tr>';
                selectedFields.forEach(field => {
                    var value = row[field];
                    html += '<td>' + this.formatCellValue(value, field) + '</td>';
                });
                html += '</tr>';
            });

            html += '</tbody></table>';
            return html;
        },

        // Format field name for display
        formatFieldName: function(fieldName) {
            return fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        },

        // Format cell value for display
        formatCellValue: function(value, fieldName) {
            if (value === null || value === undefined) return '-';

            if (fieldName.includes('date') || fieldName.includes('time')) {
                return new Date(value).toLocaleDateString();
            }

            if (typeof value === 'number') {
                return value.toLocaleString();
            }

            if (typeof value === 'string' && value.length > 50) {
                return value.substring(0, 47) + '...';
            }

            return value.toString();
        },

        // Render chart using Chart.js (if available) or fallback
        renderChart: function(chartId, data, config) {
            var canvas = document.getElementById(chartId);
            if (!canvas) return;

            var ctx = canvas.getContext('2d');
            var chartType = config.chartType || 'bar';
            var chartData = this.prepareChartData(data, config);

            // Simple chart rendering without Chart.js dependency
            this.renderSimpleChart(ctx, chartType, chartData, config);
        },

        // Prepare chart data based on configuration
        prepareChartData: function(data, config) {
            if (!Array.isArray(data) || data.length === 0) {
                return { labels: [], datasets: [{ data: [] }] };
            }

            var groupField = config.groupField || Object.keys(data[0])[0];
            var valueField = config.valueField || 'count';

            // Group data by the specified field
            var grouped = {};
            data.forEach(item => {
                var key = item[groupField] || 'Unknown';
                if (!grouped[key]) {
                    grouped[key] = [];
                }
                grouped[key].push(item);
            });

            // Prepare chart data
            var labels = Object.keys(grouped);
            var values = labels.map(label => {
                if (valueField === 'count') {
                    return grouped[label].length;
                } else {
                    return grouped[label].reduce((sum, item) => sum + (parseFloat(item[valueField]) || 0), 0);
                }
            });

            return {
                labels: labels,
                datasets: [{
                    label: config.title || 'Data',
                    data: values,
                    backgroundColor: this.generateColors(labels.length),
                    borderColor: '#667eea',
                    borderWidth: 1
                }]
            };
        },

        // Generate colors for chart
        generateColors: function(count) {
            var colors = [
                '#667eea', '#764ba2', '#f093fb', '#f5576c',
                '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
                '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'
            ];

            var result = [];
            for (var i = 0; i < count; i++) {
                result.push(colors[i % colors.length]);
            }
            return result;
        },

        // Render simple chart without external dependencies
        renderSimpleChart: function(ctx, type, data, config) {
            var canvas = ctx.canvas;
            var width = canvas.width;
            var height = canvas.height;

            // Clear canvas
            ctx.clearRect(0, 0, width, height);

            if (!data.labels || data.labels.length === 0) {
                // Draw "No data" message
                ctx.fillStyle = '#666';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('No data available', width / 2, height / 2);
                return;
            }

            switch (type) {
                case 'bar':
                    this.renderBarChart(ctx, data, width, height);
                    break;
                case 'line':
                    this.renderLineChart(ctx, data, width, height);
                    break;
                case 'pie':
                    this.renderPieChart(ctx, data, width, height);
                    break;
                default:
                    this.renderBarChart(ctx, data, width, height);
            }
        },

        // Render bar chart
        renderBarChart: function(ctx, data, width, height) {
            var padding = 40;
            var chartWidth = width - padding * 2;
            var chartHeight = height - padding * 2;
            var barWidth = chartWidth / data.labels.length * 0.8;
            var maxValue = Math.max(...data.datasets[0].data);

            // Draw bars
            data.datasets[0].data.forEach((value, index) => {
                var barHeight = (value / maxValue) * chartHeight;
                var x = padding + (index * chartWidth / data.labels.length) + (chartWidth / data.labels.length - barWidth) / 2;
                var y = height - padding - barHeight;

                ctx.fillStyle = data.datasets[0].backgroundColor[index] || '#667eea';
                ctx.fillRect(x, y, barWidth, barHeight);

                // Draw value on top of bar
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(value, x + barWidth / 2, y - 5);

                // Draw label
                ctx.fillText(data.labels[index], x + barWidth / 2, height - padding + 15);
            });
        },

        // Render line chart
        renderLineChart: function(ctx, data, width, height) {
            var padding = 40;
            var chartWidth = width - padding * 2;
            var chartHeight = height - padding * 2;
            var maxValue = Math.max(...data.datasets[0].data);

            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;
            ctx.beginPath();

            data.datasets[0].data.forEach((value, index) => {
                var x = padding + (index * chartWidth / (data.labels.length - 1));
                var y = height - padding - (value / maxValue) * chartHeight;

                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }

                // Draw point
                ctx.fillStyle = '#667eea';
                ctx.beginPath();
                ctx.arc(x, y, 3, 0, 2 * Math.PI);
                ctx.fill();

                // Draw value
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(value, x, y - 10);

                // Draw label
                ctx.fillText(data.labels[index], x, height - padding + 15);
            });

            ctx.stroke();
        },

        // Render pie chart
        renderPieChart: function(ctx, data, width, height) {
            var centerX = width / 2;
            var centerY = height / 2;
            var radius = Math.min(width, height) / 2 - 40;
            var total = data.datasets[0].data.reduce((sum, value) => sum + value, 0);

            var currentAngle = -Math.PI / 2; // Start from top

            data.datasets[0].data.forEach((value, index) => {
                var sliceAngle = (value / total) * 2 * Math.PI;

                // Draw slice
                ctx.fillStyle = data.datasets[0].backgroundColor[index] || '#667eea';
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
                ctx.closePath();
                ctx.fill();

                // Draw label
                var labelAngle = currentAngle + sliceAngle / 2;
                var labelX = centerX + Math.cos(labelAngle) * (radius + 20);
                var labelY = centerY + Math.sin(labelAngle) * (radius + 20);

                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(data.labels[index], labelX, labelY);
                ctx.fillText(value, labelX, labelY + 15);

                currentAngle += sliceAngle;
            });
        },

        // Remove widget
        removeWidget: function(widgetId) {
            if (confirm('Are you sure you want to remove this widget?')) {
                $('.dab-widget[data-widget-id="' + widgetId + '"]').remove();
                delete this.state.widgets[widgetId];

                // Show drop zone if no widgets left
                if (Object.keys(this.state.widgets).length === 0) {
                    $('.dab-drop-zone').show();
                }

                // Mark dashboard as modified
                $('#canvas-status').text('Modified').removeClass('saved').addClass('draft');
            }
        },

        // Edit widget
        editWidget: function(widgetId) {
            var self = this;
            var widget = this.state.widgets[widgetId];

            if (!widget) {
                this.showError('Widget not found');
                return;
            }

            // Create edit modal
            this.showEditWidgetModal(widget);
        },

        // Show edit widget modal
        showEditWidgetModal: function(widget) {
            var self = this;

            // Remove existing modal if any
            $('.dab-edit-widget-modal').remove();

            // Get available data sources
            this.loadDataSources(function(dataSources) {
                var dataSourceOptions = '';
                if (dataSources && dataSources.length > 0) {
                    dataSources.forEach(function(source) {
                        var selected = widget.dataSource === source.id ? 'selected' : '';
                        var sourceType = source.type ? ` (${source.type})` : '';
                        dataSourceOptions += `<option value="${source.id}" data-table-slug="${source.table_slug || ''}" ${selected}>${source.name}${sourceType}</option>`;
                    });
                } else {
                    dataSourceOptions = '<option value="">No data sources available</option>';
                }

                // Get standard configuration for widget type
                var standardConfig = self.getStandardWidgetConfig(widget.type);

                var modalHtml = `
                    <div class="dab-edit-widget-modal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;">
                        <div class="dab-modal-content" style="background: white; border-radius: 8px; padding: 20px; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto;">
                            <div class="dab-modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee;">
                                <h3 style="margin: 0;">Edit ${widget.title}</h3>
                                <button type="button" class="dab-modal-close" style="background: none; border: none; font-size: 20px; cursor: pointer; color: #666;">&times;</button>
                            </div>
                            <div class="dab-modal-body">
                                <div class="dab-form-group" style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Widget Title:</label>
                                    <input type="text" id="edit-widget-title" value="${widget.title}" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                                <div class="dab-form-group" style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Widget Type:</label>
                                    <select id="edit-widget-type" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                                        <option value="metric" ${widget.type === 'metric' ? 'selected' : ''}>Metric Card</option>
                                        <option value="chart" ${widget.type === 'chart' ? 'selected' : ''}>Chart Widget</option>
                                        <option value="table" ${widget.type === 'table' ? 'selected' : ''}>Data Table</option>
                                        <option value="gauge" ${widget.type === 'gauge' ? 'selected' : ''}>Gauge Chart</option>
                                        <option value="map" ${widget.type === 'map' ? 'selected' : ''}>Map Widget</option>
                                        <option value="text" ${widget.type === 'text' ? 'selected' : ''}>Text Widget</option>
                                    </select>
                                </div>
                                <div class="dab-form-group" style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Data Source:</label>
                                    <select id="edit-widget-datasource" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                                        <option value="">Select a data source...</option>
                                        ${dataSourceOptions}
                                    </select>
                                </div>

                                <div class="dab-form-group" id="available-fields-section" style="margin-bottom: 15px; display: none;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Available Fields:</label>
                                    <div id="available-fields-list" style="max-height: 120px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px; padding: 8px; background: #f9f9f9;">
                                        <div style="text-align: center; color: #666; padding: 20px;">Select a data source to see available fields</div>
                                    </div>
                                </div>
                                <div class="dab-form-group" style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Refresh Interval:</label>
                                    <select id="edit-widget-refresh" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                                        <option value="0" ${(widget.refreshInterval || 0) == 0 ? 'selected' : ''}>Manual only</option>
                                        <option value="30" ${widget.refreshInterval == 30 ? 'selected' : ''}>30 seconds</option>
                                        <option value="60" ${widget.refreshInterval == 60 ? 'selected' : ''}>1 minute</option>
                                        <option value="300" ${widget.refreshInterval == 300 ? 'selected' : ''}>5 minutes</option>
                                        <option value="900" ${widget.refreshInterval == 900 ? 'selected' : ''}>15 minutes</option>
                                    </select>
                                </div>

                                <!-- Standard Configuration Section -->
                                <div class="dab-form-section" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid #667eea;">
                                    <h4 style="margin: 0 0 15px 0; color: #333; font-size: 14px;">
                                        <span class="dashicons dashicons-admin-settings" style="margin-right: 5px;"></span>
                                        Standard Configuration for ${widget.type.charAt(0).toUpperCase() + widget.type.slice(1)} Widget
                                    </h4>
                                    <div style="font-size: 13px; color: #666; line-height: 1.4;">
                                        ${standardConfig.description}
                                    </div>

                                    ${widget.type === 'metric' ? `
                                        <div class="dab-form-group" style="margin: 15px 0 10px 0;">
                                            <label style="display: block; margin-bottom: 5px; font-weight: 500; font-size: 13px;">Metric Field:</label>
                                            <select id="edit-widget-metric-field" class="field-selector" style="width: 100%; padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;">
                                                <option value="">Select field to analyze...</option>
                                                <option value="id" ${(widget.metricField || 'id') === 'id' ? 'selected' : ''}>Record Count (ID)</option>
                                            </select>
                                        </div>
                                        <div class="dab-form-group" style="margin: 10px 0;">
                                            <label style="display: block; margin-bottom: 5px; font-weight: 500; font-size: 13px;">Aggregation:</label>
                                            <select id="edit-widget-aggregation" style="width: 100%; padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;">
                                                <option value="count" ${(widget.aggregation || 'count') === 'count' ? 'selected' : ''}>Count Records</option>
                                                <option value="sum" ${widget.aggregation === 'sum' ? 'selected' : ''}>Sum Values</option>
                                                <option value="avg" ${widget.aggregation === 'avg' ? 'selected' : ''}>Average</option>
                                                <option value="max" ${widget.aggregation === 'max' ? 'selected' : ''}>Maximum</option>
                                                <option value="min" ${widget.aggregation === 'min' ? 'selected' : ''}>Minimum</option>
                                            </select>
                                        </div>
                                    ` : ''}

                                    ${widget.type === 'chart' ? `
                                        <div class="dab-form-group" style="margin: 15px 0 10px 0;">
                                            <label style="display: block; margin-bottom: 5px; font-weight: 500; font-size: 13px;">Chart Type:</label>
                                            <select id="edit-widget-chart-type" style="width: 100%; padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;">
                                                <option value="bar" ${(widget.chartType || 'bar') === 'bar' ? 'selected' : ''}>Bar Chart</option>
                                                <option value="line" ${widget.chartType === 'line' ? 'selected' : ''}>Line Chart</option>
                                                <option value="pie" ${widget.chartType === 'pie' ? 'selected' : ''}>Pie Chart</option>
                                                <option value="doughnut" ${widget.chartType === 'doughnut' ? 'selected' : ''}>Doughnut Chart</option>
                                            </select>
                                        </div>
                                        <div class="dab-form-group" style="margin: 10px 0;">
                                            <label style="display: block; margin-bottom: 5px; font-weight: 500; font-size: 13px;">Group By Field:</label>
                                            <select id="edit-widget-group-field" class="field-selector" style="width: 100%; padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;">
                                                <option value="">Select field to group by...</option>
                                            </select>
                                        </div>
                                        <div class="dab-form-group" style="margin: 10px 0;">
                                            <label style="display: block; margin-bottom: 5px; font-weight: 500; font-size: 13px;">Value Field (optional):</label>
                                            <select id="edit-widget-value-field-chart" class="field-selector" style="width: 100%; padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;">
                                                <option value="">Count records (default)</option>
                                            </select>
                                        </div>
                                    ` : ''}

                                    ${widget.type === 'table' ? `
                                        <div class="dab-form-group" style="margin: 15px 0 10px 0;">
                                            <label style="display: block; margin-bottom: 5px; font-weight: 500; font-size: 13px;">Display Fields:</label>
                                            <div id="table-fields-selection" style="max-height: 100px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px; padding: 8px; background: white;">
                                                <div style="text-align: center; color: #666; padding: 10px;">Select data source to choose fields</div>
                                            </div>
                                        </div>
                                        <div class="dab-form-group" style="margin: 10px 0;">
                                            <label style="display: block; margin-bottom: 5px; font-weight: 500; font-size: 13px;">Records Limit:</label>
                                            <select id="edit-widget-limit" style="width: 100%; padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;">
                                                <option value="10" ${(widget.recordLimit || 10) == 10 ? 'selected' : ''}>10 records</option>
                                                <option value="25" ${widget.recordLimit == 25 ? 'selected' : ''}>25 records</option>
                                                <option value="50" ${widget.recordLimit == 50 ? 'selected' : ''}>50 records</option>
                                                <option value="100" ${widget.recordLimit == 100 ? 'selected' : ''}>100 records</option>
                                            </select>
                                        </div>
                                        <div class="dab-form-group" style="margin: 10px 0;">
                                            <label style="display: block; margin-bottom: 5px; font-weight: 500; font-size: 13px;">Sort By:</label>
                                            <select id="edit-widget-sort-field" class="field-selector" style="width: 100%; padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;">
                                                <option value="id" ${(widget.sortField || 'id') === 'id' ? 'selected' : ''}>ID (newest first)</option>
                                            </select>
                                        </div>
                                    ` : ''}

                                    ${widget.type === 'gauge' ? `
                                        <div class="dab-form-group" style="margin: 15px 0 10px 0;">
                                            <label style="display: block; margin-bottom: 5px; font-weight: 500; font-size: 13px;">Value Field:</label>
                                            <select id="edit-widget-value-field" class="field-selector" style="width: 100%; padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;">
                                                <option value="">Select field for gauge value...</option>
                                                <option value="id" ${(widget.valueField || 'id') === 'id' ? 'selected' : ''}>Record Count</option>
                                            </select>
                                        </div>
                                        <div class="dab-form-group" style="margin: 10px 0;">
                                            <label style="display: block; margin-bottom: 5px; font-weight: 500; font-size: 13px;">Maximum Value:</label>
                                            <input type="number" id="edit-widget-max-value" value="${widget.maxValue || 100}" min="1" style="width: 100%; padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;">
                                        </div>
                                    ` : ''}
                                </div>
                                <div class="dab-form-group" style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Width (px):</label>
                                    <input type="number" id="edit-widget-width" value="${widget.width}" min="150" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                                <div class="dab-form-group" style="margin-bottom: 20px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Height (px):</label>
                                    <input type="number" id="edit-widget-height" value="${widget.height}" min="100" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                            </div>
                            <div class="dab-modal-footer" style="display: flex; justify-content: flex-end; gap: 10px; padding-top: 15px; border-top: 1px solid #eee;">
                                <button type="button" class="button dab-modal-cancel">Cancel</button>
                                <button type="button" class="button button-primary dab-save-widget-changes" data-widget-id="${widget.id}">Save Changes</button>
                            </div>
                        </div>
                    </div>
                `;

                $('body').append(modalHtml);

                // Bind modal events
                $('.dab-modal-close, .dab-modal-cancel').on('click', function() {
                    $('.dab-edit-widget-modal').remove();
                });

                $('.dab-edit-widget-modal').on('click', function(e) {
                    if (e.target === this) {
                        $(this).remove();
                    }
                });

                $('.dab-save-widget-changes').on('click', function() {
                    self.saveWidgetChanges($(this).data('widget-id'));
                });

                // Handle data source selection
                $('#edit-widget-datasource').on('change', function() {
                    var selectedDataSource = $(this).val();
                    if (selectedDataSource) {
                        self.loadTableFields(selectedDataSource);
                    } else {
                        self.clearFieldSelectors();
                    }
                });
            });
        },

        // Load available data sources (actual database tables)
        loadDataSources: function(callback) {
            var self = this;

            // Return cached data sources if available
            if (this.cachedDataSources) {
                callback(this.cachedDataSources);
                return;
            }

            // Get actual database tables from the plugin
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_get_analytics_data_sources',
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success && response.data) {
                        self.cachedDataSources = response.data;
                        callback(response.data);
                    } else {
                        // Fallback to mock data if AJAX fails
                        self.loadMockDataSources(callback);
                    }
                },
                error: function(xhr, status, error) {
                    // Fallback to mock data if AJAX fails
                    self.loadMockDataSources(callback);
                }
            });
        },

        // Load mock data sources as fallback
        loadMockDataSources: function(callback) {
            var self = this;
            var mockDataSources = [
                { id: 'wp_users', name: 'WordPress Users', table_slug: 'wp_users', type: 'wordpress' },
                { id: 'wp_posts', name: 'WordPress Posts', table_slug: 'wp_posts', type: 'wordpress' },
                { id: 'wp_comments', name: 'WordPress Comments', table_slug: 'wp_comments', type: 'wordpress' },
                { id: 'sample_table_1', name: 'Sample Table 1', table_slug: 'sample_table_1', type: 'custom' },
                { id: 'sample_table_2', name: 'Sample Table 2', table_slug: 'sample_table_2', type: 'custom' }
            ];

            // Cache the mock data sources
            this.cachedDataSources = mockDataSources;

            setTimeout(function() {
                callback(mockDataSources);
            }, 100);
        },

        // Load table fields for selected data source
        loadTableFields: function(dataSourceId) {
            var self = this;

            // Show loading in available fields section
            $('#available-fields-section').show();
            $('#available-fields-list').html('<div style="text-align: center; color: #666; padding: 20px;"><span class="dashicons dashicons-update" style="animation: spin 1s linear infinite;"></span> Loading fields...</div>');

            // Get table fields from server
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'dab_get_table_fields',
                    data_source_id: dataSourceId,
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success && response.data) {
                        self.displayTableFields(response.data);
                        self.populateFieldSelectors(response.data);
                    } else {
                        self.displayFieldsError('Failed to load fields');
                    }
                },
                error: function() {
                    self.displayFieldsError('Network error while loading fields');
                }
            });
        },

        // Display table fields in the available fields section
        displayTableFields: function(fields) {
            var self = this;
            var fieldsHtml = '';

            if (fields && fields.length > 0) {
                fieldsHtml = '<div style="font-size: 12px; color: #666; margin-bottom: 8px;">Available fields in this table:</div>';
                fields.forEach(function(field) {
                    var fieldType = field.field_type || field.type || 'text';
                    var fieldIcon = self.getFieldIcon(fieldType);
                    fieldsHtml += '<div style="display: flex; align-items: center; padding: 4px 0; border-bottom: 1px solid #f0f0f0;">';
                    fieldsHtml += '<span class="dashicons ' + fieldIcon + '" style="font-size: 14px; margin-right: 8px; color: #667eea;"></span>';
                    fieldsHtml += '<span style="font-weight: 500; margin-right: 8px;">' + (field.field_label || field.label || field.name) + '</span>';
                    fieldsHtml += '<span style="font-size: 11px; color: #999;">(' + fieldType + ')</span>';
                    fieldsHtml += '</div>';
                });
            } else {
                fieldsHtml = '<div style="text-align: center; color: #666; padding: 20px;">No fields found in this table</div>';
            }

            $('#available-fields-list').html(fieldsHtml);
        },

        // Populate field selector dropdowns
        populateFieldSelectors: function(fields) {
            var self = this;

            // Clear existing options (except default ones)
            $('.field-selector').each(function() {
                var $select = $(this);
                var defaultOption = $select.find('option:first');
                $select.empty().append(defaultOption);
            });

            // Add field options to all field selectors
            if (fields && fields.length > 0) {
                fields.forEach(function(field) {
                    var fieldValue = field.field_slug || field.slug || field.name;
                    var fieldLabel = field.field_label || field.label || field.name;
                    var fieldType = field.field_type || field.type || 'text';

                    $('.field-selector').each(function() {
                        $(this).append('<option value="' + fieldValue + '">' + fieldLabel + ' (' + fieldType + ')</option>');
                    });
                });
            }

            // Update table fields selection for table widgets
            self.updateTableFieldsSelection(fields);
        },

        // Update table fields selection (checkboxes for table widget)
        updateTableFieldsSelection: function(fields) {
            var self = this;
            var fieldsHtml = '';

            if (fields && fields.length > 0) {
                fields.forEach(function(field) {
                    var fieldValue = field.field_slug || field.slug || field.name;
                    var fieldLabel = field.field_label || field.label || field.name;
                    var fieldType = field.field_type || field.type || 'text';
                    var fieldIcon = self.getFieldIcon(fieldType);

                    fieldsHtml += '<label style="display: flex; align-items: center; padding: 6px 0; cursor: pointer;">';
                    fieldsHtml += '<input type="checkbox" value="' + fieldValue + '" style="margin-right: 8px;" checked>';
                    fieldsHtml += '<span class="dashicons ' + fieldIcon + '" style="font-size: 14px; margin-right: 8px; color: #667eea;"></span>';
                    fieldsHtml += '<span style="font-weight: 500; margin-right: 8px;">' + fieldLabel + '</span>';
                    fieldsHtml += '<span style="font-size: 11px; color: #999;">(' + fieldType + ')</span>';
                    fieldsHtml += '</label>';
                });
            } else {
                fieldsHtml = '<div style="text-align: center; color: #666; padding: 10px;">No fields available</div>';
            }

            $('#table-fields-selection').html(fieldsHtml);
        },

        // Get field icon based on field type
        getFieldIcon: function(fieldType) {
            var icons = {
                'text': 'dashicons-text',
                'textarea': 'dashicons-text',
                'number': 'dashicons-calculator',
                'email': 'dashicons-email',
                'url': 'dashicons-admin-links',
                'date': 'dashicons-calendar-alt',
                'datetime': 'dashicons-clock',
                'select': 'dashicons-list-view',
                'checkbox': 'dashicons-yes-alt',
                'radio': 'dashicons-marker',
                'file': 'dashicons-paperclip',
                'image': 'dashicons-format-image',
                'id': 'dashicons-admin-network',
                'created_at': 'dashicons-calendar',
                'updated_at': 'dashicons-update'
            };
            return icons[fieldType] || 'dashicons-admin-generic';
        },

        // Display fields loading error
        displayFieldsError: function(message) {
            $('#available-fields-list').html('<div style="text-align: center; color: #d63638; padding: 20px;"><span class="dashicons dashicons-warning"></span> ' + message + '</div>');
        },

        // Clear field selectors
        clearFieldSelectors: function() {
            $('#available-fields-section').hide();
            $('.field-selector').each(function() {
                var $select = $(this);
                var defaultOption = $select.find('option:first');
                $select.empty().append(defaultOption);
            });
            $('#table-fields-selection').html('<div style="text-align: center; color: #666; padding: 10px;">Select data source to choose fields</div>');
        },

        // Get standard widget configuration
        getStandardWidgetConfig: function(widgetType) {
            var configs = {
                'metric': {
                    description: 'Displays a single numeric value from your data source. Perfect for showing totals, counts, averages, or other key performance indicators.',
                    defaultFields: ['metricField', 'aggregation'],
                    example: 'Total Records: 1,234'
                },
                'chart': {
                    description: 'Visualizes data trends and patterns using various chart types. Great for showing data distribution, comparisons, and time-based analytics.',
                    defaultFields: ['chartType', 'groupField'],
                    example: 'Bar chart showing records by status'
                },
                'table': {
                    description: 'Shows detailed tabular data from your source. Ideal for displaying lists of records with multiple columns and sorting capabilities.',
                    defaultFields: ['recordLimit', 'sortField'],
                    example: 'Latest 25 records sorted by date'
                },
                'gauge': {
                    description: 'Displays progress or percentage values in a circular gauge format. Perfect for showing completion rates, scores, or progress indicators.',
                    defaultFields: ['valueField', 'maxValue'],
                    example: 'Approval rate: 75% of 100%'
                },
                'map': {
                    description: 'Shows geographic data visualization. Great for displaying location-based information and regional analytics.',
                    defaultFields: ['locationField'],
                    example: 'Geographic distribution of records'
                },
                'text': {
                    description: 'Displays custom text content or formatted data summaries. Useful for descriptions, instructions, or custom formatted information.',
                    defaultFields: ['textContent'],
                    example: 'Custom text with dynamic data'
                }
            };

            return configs[widgetType] || {
                description: 'Standard widget configuration',
                defaultFields: [],
                example: 'Widget preview'
            };
        },

        // Save widget changes
        saveWidgetChanges: function(widgetId) {
            var widget = this.state.widgets[widgetId];
            if (!widget) return;

            var newTitle = $('#edit-widget-title').val();
            var newType = $('#edit-widget-type').val();
            var newDataSource = $('#edit-widget-datasource').val();
            var newRefreshInterval = parseInt($('#edit-widget-refresh').val());
            var newWidth = parseInt($('#edit-widget-width').val());
            var newHeight = parseInt($('#edit-widget-height').val());

            // Update basic widget properties
            widget.title = newTitle;
            widget.type = newType;
            widget.dataSource = newDataSource;
            widget.refreshInterval = newRefreshInterval;
            widget.width = newWidth;
            widget.height = newHeight;

            // Update widget-specific configuration
            switch (newType) {
                case 'metric':
                    widget.metricField = $('#edit-widget-metric-field').val();
                    widget.aggregation = $('#edit-widget-aggregation').val();
                    break;
                case 'chart':
                    widget.chartType = $('#edit-widget-chart-type').val();
                    widget.groupField = $('#edit-widget-group-field').val();
                    break;
                case 'table':
                    widget.recordLimit = parseInt($('#edit-widget-limit').val());
                    widget.sortField = $('#edit-widget-sort-field').val();
                    break;
                case 'gauge':
                    widget.valueField = $('#edit-widget-value-field').val();
                    widget.maxValue = parseInt($('#edit-widget-max-value').val());
                    break;
            }

            // Update widget in DOM
            var $widget = $('.dab-widget[data-widget-id="' + widgetId + '"]');
            $widget.attr('data-widget-type', newType);
            $widget.find('.dab-widget-title').html('<span class="dashicons ' + this.getWidgetIcon(newType) + '" style="margin-right: 5px;"></span>' + newTitle);
            $widget.css({
                width: newWidth + 'px',
                height: newHeight + 'px'
            });

            // Reload widget content
            this.loadWidgetContent(widgetId);

            // Close modal
            $('.dab-edit-widget-modal').remove();

            // Mark dashboard as modified
            $('#canvas-status').text('Modified').removeClass('saved').addClass('draft');

            this.showSuccess('Widget updated successfully');
        },

        // Preview dashboard
        previewDashboard: function() {
            var self = this;

            // Create preview modal
            this.showPreviewModal();
        },

        // Show preview modal
        showPreviewModal: function() {
            var self = this;

            // Remove existing modal if any
            $('.dab-preview-modal').remove();

            // Get current dashboard content
            var dashboardContent = this.generatePreviewContent();

            var modalHtml = `
                <div class="dab-preview-modal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 10000; display: flex; align-items: center; justify-content: center;">
                    <div class="dab-modal-content" style="background: white; border-radius: 8px; padding: 0; max-width: 90%; width: 1200px; max-height: 90vh; overflow: hidden; display: flex; flex-direction: column;">
                        <div class="dab-modal-header" style="display: flex; justify-content: space-between; align-items: center; padding: 20px; border-bottom: 1px solid #eee;">
                            <h3 style="margin: 0;">Dashboard Preview</h3>
                            <button type="button" class="dab-modal-close" style="background: none; border: none; font-size: 20px; cursor: pointer; color: #666;">&times;</button>
                        </div>
                        <div class="dab-modal-body" style="flex: 1; overflow: auto; padding: 20px; background: #fafafa;">
                            ${dashboardContent}
                        </div>
                        <div class="dab-modal-footer" style="display: flex; justify-content: flex-end; gap: 10px; padding: 20px; border-top: 1px solid #eee;">
                            <button type="button" class="button dab-modal-close">Close Preview</button>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modalHtml);

            // Bind modal events
            $('.dab-modal-close').on('click', function() {
                $('.dab-preview-modal').remove();
            });

            $('.dab-preview-modal').on('click', function(e) {
                if (e.target === this) {
                    $(this).remove();
                }
            });
        },

        // Generate preview content
        generatePreviewContent: function() {
            var dashboardName = $('#dashboard-name').val() || 'Untitled Dashboard';
            var dashboardDescription = $('#dashboard-description').val() || 'No description provided';

            var html = `
                <div style="background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                    <h2 style="margin: 0 0 10px 0; color: #333;">${dashboardName}</h2>
                    <p style="margin: 0; color: #666;">${dashboardDescription}</p>
                </div>
                <div style="position: relative; background: #fafafa; border-radius: 8px; min-height: 400px; padding: 20px;">
            `;

            // Add widgets to preview
            if (Object.keys(this.state.widgets).length > 0) {
                for (var widgetId in this.state.widgets) {
                    var widget = this.state.widgets[widgetId];
                    html += `
                        <div style="position: absolute; left: ${widget.x}px; top: ${widget.y}px; width: ${widget.width}px; height: ${widget.height}px; background: white; border: 1px solid #ddd; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #eee; background: #f8f9fa; border-radius: 6px 6px 0 0;">
                                <h4 style="margin: 0; font-size: 14px; color: #333;">
                                    <span class="dashicons ${this.getWidgetIcon(widget.type)}" style="margin-right: 5px;"></span>${widget.title}
                                </h4>
                            </div>
                            <div style="padding: 15px; height: calc(100% - 50px); overflow: auto;">
                                ${this.getPreviewWidgetContent(widget.type)}
                            </div>
                        </div>
                    `;
                }
            } else {
                html += `
                    <div style="display: flex; align-items: center; justify-content: center; height: 300px; color: #999; text-align: center;">
                        <div>
                            <span class="dashicons dashicons-plus-alt" style="font-size: 48px; margin-bottom: 10px;"></span>
                            <p>No widgets added to this dashboard yet.</p>
                        </div>
                    </div>
                `;
            }

            html += '</div>';
            return html;
        },

        // Get preview widget content
        getPreviewWidgetContent: function(type) {
            switch (type) {
                case 'metric':
                    return '<div style="text-align: center;"><div style="font-size: 2em; font-weight: bold; color: #667eea;">42</div><div style="color: #666; margin-top: 5px;">Sample Metric</div></div>';
                case 'chart':
                    return '<div style="text-align: center; padding: 20px;"><div style="background: #f0f0f0; height: 80px; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #666;"><span class="dashicons dashicons-chart-bar" style="font-size: 24px; margin-right: 10px;"></span>Chart Preview</div></div>';
                case 'table':
                    return '<table style="width: 100%; border-collapse: collapse;"><thead><tr style="background: #f8f9fa;"><th style="padding: 8px; border: 1px solid #ddd;">Column 1</th><th style="padding: 8px; border: 1px solid #ddd;">Column 2</th></tr></thead><tbody><tr><td style="padding: 8px; border: 1px solid #ddd;">Data 1</td><td style="padding: 8px; border: 1px solid #ddd;">Data 2</td></tr></tbody></table>';
                case 'gauge':
                    return '<div style="text-align: center; padding: 20px;"><div style="width: 80px; height: 80px; border: 8px solid #667eea; border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center; color: #667eea; font-weight: bold;">75%</div></div>';
                case 'map':
                    return '<div style="text-align: center; padding: 20px;"><div style="background: #e8f4f8; height: 80px; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #666;"><span class="dashicons dashicons-location" style="font-size: 24px; margin-right: 10px;"></span>Map Preview</div></div>';
                case 'text':
                    return '<p style="margin: 0; color: #333;">This is a sample text widget. You can add custom content here.</p>';
                default:
                    return '<div style="text-align: center; color: #666; padding: 20px;">Unknown widget type: ' + type + '</div>';
            }
        },

        // Export dashboard
        exportDashboard: function() {
            var self = this;
            var dashboardData = this.collectDashboardData();

            if (!this.validateDashboardData(dashboardData)) {
                return;
            }

            // Create export data
            var exportData = {
                name: dashboardData.name,
                description: dashboardData.description,
                settings: {
                    refresh_interval: dashboardData.refresh_interval,
                    auto_refresh: dashboardData.auto_refresh,
                    is_public: dashboardData.is_public
                },
                widgets: this.state.widgets,
                layout: dashboardData.layout_config,
                theme: dashboardData.theme_config,
                exported_at: new Date().toISOString(),
                version: '1.0'
            };

            // Create and download file
            var dataStr = JSON.stringify(exportData, null, 2);
            var dataBlob = new Blob([dataStr], {type: 'application/json'});
            var url = URL.createObjectURL(dataBlob);

            var link = document.createElement('a');
            link.href = url;
            link.download = (dashboardData.name || 'dashboard') + '_export.json';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            this.showSuccess('Dashboard exported successfully');
        },

        // Share dashboard
        shareDashboard: function() {
            alert('Dashboard sharing functionality will be implemented in a future update.');
        },

        // Filter dashboards
        filterDashboards: function() {
            // Dashboard filtering functionality
            var filter = $('#dashboard-filter').val();
            var search = $('#search-dashboards').val().toLowerCase();

            $('.dab-dashboard-card').each(function() {
                var $card = $(this);
                var title = $card.find('h3').text().toLowerCase();
                var description = $card.find('p').text().toLowerCase();
                var isPublic = $card.find('.dab-card-status').hasClass('public');

                var matchesFilter = filter === 'all' ||
                    (filter === 'public' && isPublic) ||
                    (filter === 'private' && !isPublic);

                var matchesSearch = search === '' ||
                    title.includes(search) ||
                    description.includes(search);

                if (matchesFilter && matchesSearch) {
                    $card.show();
                } else {
                    $card.hide();
                }
            });
        },

        // Toggle fullscreen
        toggleFullscreen: function() {
            if (!this.state.isFullscreen) {
                // Enter fullscreen
                var elem = document.documentElement;
                if (elem.requestFullscreen) {
                    elem.requestFullscreen();
                } else if (elem.mozRequestFullScreen) {
                    elem.mozRequestFullScreen();
                } else if (elem.webkitRequestFullscreen) {
                    elem.webkitRequestFullscreen();
                } else if (elem.msRequestFullscreen) {
                    elem.msRequestFullscreen();
                }
                this.state.isFullscreen = true;
                $('#fullscreen-toggle, #fullscreen-view').find('.dashicons').removeClass('dashicons-fullscreen-alt').addClass('dashicons-fullscreen-exit-alt');
            } else {
                // Exit fullscreen
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
                this.state.isFullscreen = false;
                $('#fullscreen-toggle, #fullscreen-view').find('.dashicons').removeClass('dashicons-fullscreen-exit-alt').addClass('dashicons-fullscreen-alt');
            }
        },

        // Update last refresh time
        updateLastRefreshTime: function() {
            var now = new Date();
            var timeString = now.toLocaleTimeString();
            $('#last-updated').text('Last updated: ' + timeString);
        },

        // Show success message
        showSuccess: function(message) {
            this.showNotification(message, 'success');
        },

        // Show error message
        showError: function(message) {
            this.showNotification(message, 'error');
        },

        // Show notification
        showNotification: function(message, type) {
            var $notification = $('<div class="dab-notification dab-notification-' + type + '">' + message + '</div>');
            $('body').append($notification);

            setTimeout(function() {
                $notification.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        if (typeof dab_admin_vars !== 'undefined') {
            DAB_AnalyticsDashboard.init();
        }
    });

})(jQuery);
