<?php
/**
 * Report Scheduler Manager
 * Phase 3: Data Intelligence & Analytics
 *
 * Handles automated report generation, scheduling, and distribution
 * with email notifications and various export formats.
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Report_Scheduler {

    /**
     * Initialize the Report Scheduler
     */
    public static function init() {
        add_action('wp_ajax_dab_schedule_report', array(__CLASS__, 'schedule_report'));
        add_action('wp_ajax_dab_update_schedule', array(__CLASS__, 'update_schedule'));
        add_action('wp_ajax_dab_delete_schedule', array(__CLASS__, 'delete_schedule'));
        add_action('wp_ajax_dab_get_scheduled_reports', array(__CLASS__, 'get_scheduled_reports'));
        add_action('wp_ajax_dab_run_scheduled_report', array(__CLASS__, 'run_scheduled_report'));
        add_action('wp_ajax_dab_pause_schedule', array(__CLASS__, 'pause_schedule'));
        add_action('wp_ajax_dab_resume_schedule', array(__CLASS__, 'resume_schedule'));

        // Cron hooks for scheduled reports
        add_action('dab_run_scheduled_report', array(__CLASS__, 'execute_scheduled_report'), 10, 1);
        add_action('dab_cleanup_old_reports', array(__CLASS__, 'cleanup_old_reports'));

        // Schedule cleanup job
        if (!wp_next_scheduled('dab_cleanup_old_reports')) {
            wp_schedule_event(time(), 'daily', 'dab_cleanup_old_reports');
        }
    }

    /**
     * Create database tables for report scheduling
     */
    public static function create_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        // Scheduled reports table
        $schedules_table = $wpdb->prefix . 'dab_report_schedules';
        $sql = "CREATE TABLE IF NOT EXISTS $schedules_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            report_id BIGINT(20) UNSIGNED NOT NULL,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            schedule_type ENUM('once', 'daily', 'weekly', 'monthly', 'custom') NOT NULL,
            schedule_config TEXT,
            export_format ENUM('pdf', 'excel', 'csv', 'html') DEFAULT 'pdf',
            email_config TEXT,
            filter_config TEXT,
            is_active TINYINT(1) DEFAULT 1,
            created_by BIGINT(20) UNSIGNED,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_run DATETIME NULL,
            next_run DATETIME NULL,
            run_count INT DEFAULT 0,
            PRIMARY KEY (id),
            KEY idx_report_id (report_id),
            KEY idx_schedule_type (schedule_type),
            KEY idx_is_active (is_active),
            KEY idx_next_run (next_run)
        ) $charset_collate;";

        // Report executions history table
        $executions_table = $wpdb->prefix . 'dab_scheduled_executions';
        $sql .= "CREATE TABLE IF NOT EXISTS $executions_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            schedule_id BIGINT(20) UNSIGNED NOT NULL,
            execution_status ENUM('success', 'failed', 'partial') NOT NULL,
            execution_time FLOAT,
            row_count INT,
            file_path VARCHAR(500),
            file_size BIGINT,
            error_message TEXT,
            email_sent TINYINT(1) DEFAULT 0,
            email_recipients TEXT,
            executed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_schedule_id (schedule_id),
            KEY idx_execution_status (execution_status),
            KEY idx_executed_at (executed_at),
            FOREIGN KEY (schedule_id) REFERENCES $schedules_table(id) ON DELETE CASCADE
        ) $charset_collate;";

        // Email templates table
        $templates_table = $wpdb->prefix . 'dab_email_templates';
        $sql .= "CREATE TABLE IF NOT EXISTS $templates_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            subject VARCHAR(500) NOT NULL,
            body_html LONGTEXT,
            body_text LONGTEXT,
            template_type ENUM('report_success', 'report_failed', 'custom') DEFAULT 'custom',
            is_system TINYINT(1) DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_template_type (template_type),
            KEY idx_is_system (is_system)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // Insert default email templates
        self::insert_default_email_templates();
    }

    /**
     * Insert default email templates
     */
    private static function insert_default_email_templates() {
        global $wpdb;
        $templates_table = $wpdb->prefix . 'dab_email_templates';

        $default_templates = array(
            array(
                'name' => 'Report Success Notification',
                'subject' => 'Scheduled Report: {{report_name}} - Generated Successfully',
                'body_html' => '<h2>Report Generated Successfully</h2>
                    <p>Your scheduled report "{{report_name}}" has been generated successfully.</p>
                    <p><strong>Details:</strong></p>
                    <ul>
                        <li>Generated: {{execution_date}}</li>
                        <li>Records: {{row_count}}</li>
                        <li>Format: {{export_format}}</li>
                        <li>File Size: {{file_size}}</li>
                    </ul>
                    <p>The report is attached to this email.</p>',
                'body_text' => 'Report Generated Successfully

                    Your scheduled report "{{report_name}}" has been generated successfully.

                    Details:
                    - Generated: {{execution_date}}
                    - Records: {{row_count}}
                    - Format: {{export_format}}
                    - File Size: {{file_size}}

                    The report is attached to this email.',
                'template_type' => 'report_success',
                'is_system' => 1
            ),
            array(
                'name' => 'Report Failed Notification',
                'subject' => 'Scheduled Report: {{report_name}} - Generation Failed',
                'body_html' => '<h2>Report Generation Failed</h2>
                    <p>Your scheduled report "{{report_name}}" failed to generate.</p>
                    <p><strong>Error Details:</strong></p>
                    <p>{{error_message}}</p>
                    <p><strong>Execution Details:</strong></p>
                    <ul>
                        <li>Attempted: {{execution_date}}</li>
                        <li>Schedule: {{schedule_type}}</li>
                    </ul>
                    <p>Please check your report configuration and try again.</p>',
                'body_text' => 'Report Generation Failed

                    Your scheduled report "{{report_name}}" failed to generate.

                    Error Details:
                    {{error_message}}

                    Execution Details:
                    - Attempted: {{execution_date}}
                    - Schedule: {{schedule_type}}

                    Please check your report configuration and try again.',
                'template_type' => 'report_failed',
                'is_system' => 1
            )
        );

        foreach ($default_templates as $template) {
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $templates_table WHERE template_type = %s AND is_system = 1",
                $template['template_type']
            ));

            if (!$existing) {
                $wpdb->insert($templates_table, $template);
            }
        }
    }

    /**
     * Schedule a report
     */
    public static function schedule_report() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        global $wpdb;
        $schedules_table = $wpdb->prefix . 'dab_report_schedules';

        $report_id = intval($_POST['report_id']);
        $name = sanitize_text_field($_POST['name']);
        $description = sanitize_textarea_field($_POST['description']);
        $schedule_type = sanitize_text_field($_POST['schedule_type']);
        $schedule_config = $_POST['schedule_config'];
        $export_format = sanitize_text_field($_POST['export_format']);
        $email_config = $_POST['email_config'];
        $filter_config = $_POST['filter_config'];

        // Calculate next run time
        $next_run = self::calculate_next_run($schedule_type, $schedule_config);

        $data = array(
            'report_id' => $report_id,
            'name' => $name,
            'description' => $description,
            'schedule_type' => $schedule_type,
            'schedule_config' => json_encode($schedule_config),
            'export_format' => $export_format,
            'email_config' => json_encode($email_config),
            'filter_config' => json_encode($filter_config),
            'created_by' => get_current_user_id(),
            'next_run' => $next_run
        );

        $result = $wpdb->insert($schedules_table, $data);

        if ($result !== false) {
            $schedule_id = $wpdb->insert_id;

            // Schedule the WordPress cron job
            wp_schedule_single_event($next_run, 'dab_run_scheduled_report', array($schedule_id));

            wp_send_json_success(array(
                'message' => 'Report scheduled successfully',
                'schedule_id' => $schedule_id,
                'next_run' => $next_run
            ));
        } else {
            wp_send_json_error('Failed to schedule report');
        }
    }

    /**
     * Update schedule configuration
     */
    public static function update_schedule() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        global $wpdb;
        $schedules_table = $wpdb->prefix . 'dab_report_schedules';

        $schedule_id = intval($_POST['schedule_id']);
        $name = sanitize_text_field($_POST['name']);
        $description = sanitize_textarea_field($_POST['description']);
        $schedule_type = sanitize_text_field($_POST['schedule_type']);
        $schedule_config = $_POST['schedule_config'];
        $export_format = sanitize_text_field($_POST['export_format']);
        $email_config = $_POST['email_config'];
        $filter_config = $_POST['filter_config'];

        // Calculate new next run time
        $next_run = self::calculate_next_run($schedule_type, $schedule_config);

        $data = array(
            'name' => $name,
            'description' => $description,
            'schedule_type' => $schedule_type,
            'schedule_config' => json_encode($schedule_config),
            'export_format' => $export_format,
            'email_config' => json_encode($email_config),
            'filter_config' => json_encode($filter_config),
            'next_run' => $next_run
        );

        $result = $wpdb->update($schedules_table, $data, array('id' => $schedule_id));

        if ($result !== false) {
            // Clear old cron job and schedule new one
            wp_clear_scheduled_hook('dab_run_scheduled_report', array($schedule_id));
            wp_schedule_single_event($next_run, 'dab_run_scheduled_report', array($schedule_id));

            wp_send_json_success(array(
                'message' => 'Schedule updated successfully',
                'next_run' => $next_run
            ));
        } else {
            wp_send_json_error('Failed to update schedule');
        }
    }

    /**
     * Delete a schedule
     */
    public static function delete_schedule() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $schedule_id = intval($_POST['schedule_id']);

        global $wpdb;
        $schedules_table = $wpdb->prefix . 'dab_report_schedules';

        // Clear scheduled cron job
        wp_clear_scheduled_hook('dab_run_scheduled_report', array($schedule_id));

        $result = $wpdb->delete($schedules_table, array('id' => $schedule_id));

        if ($result !== false) {
            wp_send_json_success('Schedule deleted successfully');
        } else {
            wp_send_json_error('Failed to delete schedule');
        }
    }

    /**
     * Get all scheduled reports
     */
    public static function get_scheduled_reports() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        global $wpdb;
        $schedules_table = $wpdb->prefix . 'dab_report_schedules';
        $reports_table = $wpdb->prefix . 'dab_advanced_reports';

        $schedules = $wpdb->get_results("
            SELECT s.*, r.name as report_name, r.description as report_description
            FROM $schedules_table s
            LEFT JOIN $reports_table r ON s.report_id = r.id
            ORDER BY s.created_at DESC
        ");

        // Decode JSON fields
        foreach ($schedules as $schedule) {
            $schedule->schedule_config = json_decode($schedule->schedule_config, true);
            $schedule->email_config = json_decode($schedule->email_config, true);
            $schedule->filter_config = json_decode($schedule->filter_config, true);
        }

        wp_send_json_success($schedules);
    }

    /**
     * Run scheduled report manually
     */
    public static function run_scheduled_report() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $schedule_id = intval($_POST['schedule_id']);

        // Execute the report immediately
        self::execute_scheduled_report($schedule_id);
    }

    /**
     * Pause a schedule
     */
    public static function pause_schedule() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $schedule_id = intval($_POST['schedule_id']);

        global $wpdb;
        $schedules_table = $wpdb->prefix . 'dab_report_schedules';

        // Clear scheduled cron job
        wp_clear_scheduled_hook('dab_run_scheduled_report', array($schedule_id));

        $result = $wpdb->update(
            $schedules_table,
            array('is_active' => 0),
            array('id' => $schedule_id)
        );

        if ($result !== false) {
            wp_send_json_success('Schedule paused successfully');
        } else {
            wp_send_json_error('Failed to pause schedule');
        }
    }

    /**
     * Resume a schedule
     */
    public static function resume_schedule() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_admin_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $schedule_id = intval($_POST['schedule_id']);

        global $wpdb;
        $schedules_table = $wpdb->prefix . 'dab_report_schedules';

        // Get schedule details
        $schedule = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $schedules_table WHERE id = %d",
            $schedule_id
        ));

        if (!$schedule) {
            wp_send_json_error('Schedule not found');
            return;
        }

        // Calculate next run time
        $schedule_config = json_decode($schedule->schedule_config, true);
        $next_run = self::calculate_next_run($schedule->schedule_type, $schedule_config);

        // Update schedule
        $result = $wpdb->update(
            $schedules_table,
            array(
                'is_active' => 1,
                'next_run' => $next_run
            ),
            array('id' => $schedule_id)
        );

        if ($result !== false) {
            // Schedule the WordPress cron job
            wp_schedule_single_event($next_run, 'dab_run_scheduled_report', array($schedule_id));

            wp_send_json_success(array(
                'message' => 'Schedule resumed successfully',
                'next_run' => $next_run
            ));
        } else {
            wp_send_json_error('Failed to resume schedule');
        }
    }

    /**
     * Execute scheduled report (called by cron)
     */
    public static function execute_scheduled_report($schedule_id) {
        global $wpdb;
        $schedules_table = $wpdb->prefix . 'dab_report_schedules';
        $executions_table = $wpdb->prefix . 'dab_scheduled_executions';

        // Get schedule details
        $schedule = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $schedules_table WHERE id = %d AND is_active = 1",
            $schedule_id
        ));

        if (!$schedule) {
            error_log("DAB Report Scheduler: Schedule $schedule_id not found or inactive");
            return;
        }

        $start_time = microtime(true);
        $execution_data = array(
            'schedule_id' => $schedule_id,
            'execution_status' => 'failed',
            'execution_time' => 0,
            'row_count' => 0,
            'email_sent' => 0
        );

        try {
            // Generate report data
            $report_data = self::generate_report_data($schedule);
            $execution_time = microtime(true) - $start_time;

            if (empty($report_data)) {
                throw new Exception('No data returned from report query');
            }

            // Process data based on configuration
            $processed_data = self::process_report_output($report_data, $schedule);

            // Export report to file
            $file_info = self::export_report_to_file($processed_data, $schedule);

            // Send email if configured
            $email_sent = false;
            $email_config = json_decode($schedule->email_config, true);
            if (!empty($email_config['enabled']) && !empty($email_config['recipients'])) {
                $email_sent = self::send_report_email($schedule, $processed_data, $file_info);
            }

            // Update execution data
            $execution_data = array_merge($execution_data, array(
                'execution_status' => 'success',
                'execution_time' => $execution_time,
                'row_count' => count($report_data),
                'file_path' => $file_info['file_path'] ?? null,
                'file_size' => $file_info['file_size'] ?? 0,
                'email_sent' => $email_sent ? 1 : 0,
                'email_recipients' => $email_sent ? json_encode($email_config['recipients']) : null
            ));

            // Schedule next run
            $schedule_config = json_decode($schedule->schedule_config, true);
            $next_run = self::calculate_next_run($schedule->schedule_type, $schedule_config);

            // Update schedule
            $wpdb->update(
                $schedules_table,
                array(
                    'last_run' => current_time('mysql'),
                    'next_run' => $next_run,
                    'run_count' => $schedule->run_count + 1
                ),
                array('id' => $schedule_id)
            );

            // Schedule next execution
            if ($schedule->schedule_type !== 'once') {
                wp_schedule_single_event($next_run, 'dab_run_scheduled_report', array($schedule_id));
            }

        } catch (Exception $e) {
            $execution_data['error_message'] = $e->getMessage();
            error_log("DAB Report Scheduler Error: " . $e->getMessage());

            // Send failure notification
            $email_config = json_decode($schedule->email_config, true);
            if (!empty($email_config['enabled']) && !empty($email_config['recipients'])) {
                self::send_failure_notification($schedule, $e->getMessage());
            }
        }

        // Log execution
        $wpdb->insert($executions_table, $execution_data);
    }

    /**
     * Generate report data based on schedule configuration
     */
    private static function generate_report_data($schedule) {
        global $wpdb;

        // Get the base report configuration
        $reports_table = $wpdb->prefix . 'dab_advanced_reports';
        $report = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $reports_table WHERE id = %d",
            $schedule->report_id
        ));

        if (!$report) {
            throw new Exception('Base report not found');
        }

        // Parse configurations
        $data_sources = json_decode($report->data_sources, true);
        $query_config = json_decode($report->query_config, true);
        $filter_config = json_decode($schedule->filter_config, true);

        // Apply scheduled filters
        if (!empty($filter_config)) {
            $query_config = array_merge_recursive($query_config, $filter_config);
        }

        // Execute query
        return self::execute_report_query($data_sources, $query_config);
    }

    /**
     * Execute report query
     */
    private static function execute_report_query($data_sources, $query_config) {
        global $wpdb;

        if (empty($data_sources) || empty($query_config)) {
            throw new Exception('Invalid query configuration');
        }

        $primary_source = $data_sources[0];
        $table_name = $wpdb->prefix . 'dab_' . $primary_source['table_slug'];

        // Build SQL query
        $sql = self::build_sql_query($table_name, $query_config);

        // Execute query
        $results = $wpdb->get_results($sql, ARRAY_A);

        if ($wpdb->last_error) {
            throw new Exception('Database query error: ' . $wpdb->last_error);
        }

        return $results;
    }

    /**
     * Build SQL query from configuration
     */
    private static function build_sql_query($table_name, $config) {
        global $wpdb;

        $fields = $config['fields'] ?? array('*');
        $where_conditions = $config['where'] ?? array();
        $order_by = $config['order_by'] ?? array();
        $limit = $config['limit'] ?? null;

        // Build SELECT clause
        $select_fields = is_array($fields) ? implode(', ', $fields) : $fields;
        $sql = "SELECT $select_fields FROM $table_name";

        // Build WHERE clause
        if (!empty($where_conditions)) {
            $where_clauses = array();
            foreach ($where_conditions as $condition) {
                $where_clauses[] = self::build_where_condition($condition);
            }
            $sql .= " WHERE " . implode(' AND ', $where_clauses);
        }

        // Add ORDER BY
        if (!empty($order_by)) {
            $order_clauses = array();
            foreach ($order_by as $field => $direction) {
                $order_clauses[] = "$field $direction";
            }
            $sql .= " ORDER BY " . implode(', ', $order_clauses);
        }

        // Add LIMIT
        if ($limit && $limit > 0) {
            $sql .= " LIMIT " . intval($limit);
        }

        return $sql;
    }

    /**
     * Build WHERE condition
     */
    private static function build_where_condition($condition) {
        global $wpdb;

        $field = $condition['field'] ?? '';
        $operator = $condition['operator'] ?? '=';
        $value = $condition['value'] ?? '';

        switch ($operator) {
            case 'LIKE':
                return $wpdb->prepare("$field LIKE %s", '%' . $value . '%');
            case 'IN':
                $placeholders = implode(',', array_fill(0, count($value), '%s'));
                return $wpdb->prepare("$field IN ($placeholders)", $value);
            case 'BETWEEN':
                return $wpdb->prepare("$field BETWEEN %s AND %s", $value[0], $value[1]);
            case 'IS NULL':
                return "$field IS NULL";
            case 'IS NOT NULL':
                return "$field IS NOT NULL";
            default:
                return $wpdb->prepare("$field $operator %s", $value);
        }
    }

    /**
     * Process report output based on format
     */
    private static function process_report_output($data, $schedule) {
        $export_format = $schedule->export_format;

        switch ($export_format) {
            case 'csv':
                return self::process_csv_output($data);
            case 'excel':
                return self::process_excel_output($data);
            case 'pdf':
                return self::process_pdf_output($data);
            case 'html':
                return self::process_html_output($data);
            default:
                return $data;
        }
    }

    /**
     * Process CSV output
     */
    private static function process_csv_output($data) {
        if (empty($data)) {
            return '';
        }

        $output = '';
        $headers = array_keys($data[0]);

        // Add headers
        $output .= implode(',', array_map(function($header) {
            return '"' . str_replace('"', '""', $header) . '"';
        }, $headers)) . "\n";

        // Add data rows
        foreach ($data as $row) {
            $csv_row = array();
            foreach ($headers as $header) {
                $value = $row[$header] ?? '';
                $csv_row[] = '"' . str_replace('"', '""', $value) . '"';
            }
            $output .= implode(',', $csv_row) . "\n";
        }

        return $output;
    }

    /**
     * Process HTML output
     */
    private static function process_html_output($data) {
        if (empty($data)) {
            return '<p>No data available</p>';
        }

        $headers = array_keys($data[0]);

        $html = '<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">';
        $html .= '<thead><tr>';

        foreach ($headers as $header) {
            $html .= '<th style="background-color: #f5f5f5; font-weight: bold;">' . htmlspecialchars($header) . '</th>';
        }

        $html .= '</tr></thead><tbody>';

        foreach ($data as $row) {
            $html .= '<tr>';
            foreach ($headers as $header) {
                $value = $row[$header] ?? '';
                $html .= '<td>' . htmlspecialchars($value) . '</td>';
            }
            $html .= '</tr>';
        }

        $html .= '</tbody></table>';

        return $html;
    }

    /**
     * Process Excel output (basic implementation)
     */
    private static function process_excel_output($data) {
        // For now, return CSV format which can be opened in Excel
        return self::process_csv_output($data);
    }

    /**
     * Process PDF output (basic implementation)
     */
    private static function process_pdf_output($data) {
        // For now, return HTML format
        return self::process_html_output($data);
    }

    /**
     * Export report to file
     */
    private static function export_report_to_file($processed_data, $schedule) {
        $upload_dir = wp_upload_dir();
        $reports_dir = $upload_dir['basedir'] . '/dab-reports';

        // Create directory if it doesn't exist
        if (!file_exists($reports_dir)) {
            wp_mkdir_p($reports_dir);
        }

        $filename = 'report_' . $schedule->id . '_' . date('Y-m-d_H-i-s') . '.' . $schedule->export_format;
        $file_path = $reports_dir . '/' . $filename;

        // Write file based on format
        switch ($schedule->export_format) {
            case 'csv':
            case 'html':
                $bytes_written = file_put_contents($file_path, $processed_data);
                break;
            default:
                $bytes_written = file_put_contents($file_path, $processed_data);
        }

        if ($bytes_written === false) {
            throw new Exception('Failed to write report file');
        }

        return array(
            'file_path' => $file_path,
            'file_size' => filesize($file_path),
            'filename' => $filename,
            'url' => $upload_dir['baseurl'] . '/dab-reports/' . $filename
        );
    }

    /**
     * Send report email
     */
    private static function send_report_email($schedule, $processed_data, $file_info) {
        $email_config = json_decode($schedule->email_config, true);

        if (empty($email_config['recipients'])) {
            return false;
        }

        $subject = $email_config['subject'] ?? 'Scheduled Report: ' . $schedule->name;
        $message = $email_config['message'] ?? 'Please find your scheduled report attached.';

        // Replace placeholders
        $placeholders = array(
            '{{report_name}}' => $schedule->name,
            '{{execution_date}}' => current_time('mysql'),
            '{{row_count}}' => count($processed_data),
            '{{export_format}}' => strtoupper($schedule->export_format),
            '{{file_size}}' => self::format_file_size($file_info['file_size'] ?? 0)
        );

        $subject = str_replace(array_keys($placeholders), array_values($placeholders), $subject);
        $message = str_replace(array_keys($placeholders), array_values($placeholders), $message);

        $headers = array('Content-Type: text/html; charset=UTF-8');
        $attachments = array();

        if (!empty($file_info['file_path']) && file_exists($file_info['file_path'])) {
            $attachments[] = $file_info['file_path'];
        }

        $sent = false;
        foreach ($email_config['recipients'] as $recipient) {
            if (wp_mail($recipient, $subject, $message, $headers, $attachments)) {
                $sent = true;
            }
        }

        return $sent;
    }

    /**
     * Send failure notification
     */
    private static function send_failure_notification($schedule, $error_message) {
        $email_config = json_decode($schedule->email_config, true);

        if (empty($email_config['recipients'])) {
            return false;
        }

        $subject = 'Scheduled Report Failed: ' . $schedule->name;
        $message = '<h2>Report Generation Failed</h2>';
        $message .= '<p>Your scheduled report "' . $schedule->name . '" failed to generate.</p>';
        $message .= '<p><strong>Error:</strong> ' . htmlspecialchars($error_message) . '</p>';
        $message .= '<p><strong>Schedule:</strong> ' . $schedule->schedule_type . '</p>';
        $message .= '<p><strong>Time:</strong> ' . current_time('mysql') . '</p>';

        $headers = array('Content-Type: text/html; charset=UTF-8');

        foreach ($email_config['recipients'] as $recipient) {
            wp_mail($recipient, $subject, $message, $headers);
        }

        return true;
    }

    /**
     * Calculate next run time based on schedule type and configuration
     */
    private static function calculate_next_run($schedule_type, $schedule_config) {
        $now = current_time('timestamp');

        switch ($schedule_type) {
            case 'once':
                return isset($schedule_config['datetime']) ?
                    strtotime($schedule_config['datetime']) : $now;

            case 'daily':
                $time = $schedule_config['time'] ?? '09:00';
                $next_run = strtotime('tomorrow ' . $time);
                return $next_run;

            case 'weekly':
                $day = $schedule_config['day'] ?? 'monday';
                $time = $schedule_config['time'] ?? '09:00';
                $next_run = strtotime('next ' . $day . ' ' . $time);
                return $next_run;

            case 'monthly':
                $day = $schedule_config['day'] ?? 1;
                $time = $schedule_config['time'] ?? '09:00';
                $next_month = date('Y-m-01', strtotime('+1 month'));
                $next_run = strtotime($next_month . ' +' . ($day - 1) . ' days ' . $time);
                return $next_run;

            case 'custom':
                $interval = $schedule_config['interval'] ?? 3600; // 1 hour default
                return $now + $interval;

            default:
                return $now + 3600; // 1 hour default
        }
    }

    /**
     * Format file size for display
     */
    private static function format_file_size($bytes) {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Cleanup old report files
     */
    public static function cleanup_old_reports() {
        $upload_dir = wp_upload_dir();
        $reports_dir = $upload_dir['basedir'] . '/dab-reports';

        if (!file_exists($reports_dir)) {
            return;
        }

        $files = glob($reports_dir . '/*');
        $cutoff_time = time() - (30 * 24 * 60 * 60); // 30 days ago

        foreach ($files as $file) {
            if (is_file($file) && filemtime($file) < $cutoff_time) {
                unlink($file);
            }
        }

        // Also cleanup old execution records
        global $wpdb;
        $executions_table = $wpdb->prefix . 'dab_scheduled_executions';

        $wpdb->query($wpdb->prepare(
            "DELETE FROM $executions_table WHERE executed_at < DATE_SUB(NOW(), INTERVAL 90 DAY)"
        ));
    }

    /**
     * Calculate pivot aggregation
     */
    private static function calculate_pivot_aggregation($items, $field, $aggregation) {
        if (empty($items)) {
            return 0;
        }

        switch ($aggregation) {
            case 'count':
                return count($items);
            case 'sum':
                return array_sum(array_column($items, $field));
            case 'avg':
                $values = array_column($items, $field);
                return count($values) > 0 ? array_sum($values) / count($values) : 0;
            case 'max':
                return max(array_column($items, $field));
            case 'min':
                return min(array_column($items, $field));
            default:
                return count($items);
        }
    }
}
