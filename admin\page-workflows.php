<?php
/**
 * Workflow Builder Admin Page
 *
 * Visual workflow builder interface for creating automated workflows
 */

if (!defined('ABSPATH')) {
    exit;
}

// Check user permissions
if (!current_user_can('manage_options')) {
    wp_die(__('You do not have sufficient permissions to access this page.'));
}

// Handle workflow actions
$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
$workflow_id = isset($_GET['workflow_id']) ? intval($_GET['workflow_id']) : 0;

// Get workflows
global $wpdb;
$workflows_table = $wpdb->prefix . 'dab_workflows';
$workflows = $wpdb->get_results("SELECT * FROM $workflows_table ORDER BY created_at DESC");

// Get tables for trigger configuration
$tables_table = $wpdb->prefix . 'dab_tables';
$tables = $wpdb->get_results("SELECT * FROM $tables_table ORDER BY table_label");

// Get forms for trigger configuration
$forms_table = $wpdb->prefix . 'dab_forms';
$forms = $wpdb->get_results("SELECT * FROM $forms_table ORDER BY form_title");
?>

<div class="wrap dab-admin-wrap">
    <div class="dab-admin-header">
        <h1 class="dab-admin-title">
            <span class="dashicons dashicons-networking"></span>
            <?php _e('Workflow Builder', 'db-app-builder'); ?>
        </h1>
        <p class="dab-admin-subtitle">
            <?php _e('General automation engine for business processes. Create workflows that trigger on form submissions, data changes, schedules, or webhooks.', 'db-app-builder'); ?>
        </p>
        <div class="dab-workflow-info-box" style="background: #e3f2fd; border: 1px solid #2196f3; border-radius: 6px; padding: 15px; margin: 15px 0;">
            <h4 style="margin: 0 0 10px 0; color: #1976d2;">
                <span class="dashicons dashicons-info" style="margin-right: 5px;"></span>
                <?php _e('Workflow System Overview', 'db-app-builder'); ?>
            </h4>
            <p style="margin: 0; font-size: 14px; color: #333;">
                <strong><?php _e('Workflow Builder:', 'db-app-builder'); ?></strong> <?php _e('General automation (emails, data sync, notifications)', 'db-app-builder'); ?><br>
                <strong><?php _e('Approval Workflows:', 'db-app-builder'); ?></strong> <?php _e('Hierarchical approval processes (Manager → Director)', 'db-app-builder'); ?><br>
                <strong><?php _e('Pending Approvals:', 'db-app-builder'); ?></strong> <?php _e('User interface for managing approval tasks', 'db-app-builder'); ?>
            </p>
        </div>
        <div class="dab-admin-actions">
            <button type="button" class="dab-btn dab-btn-primary" id="create-workflow-btn">
                <span class="dashicons dashicons-plus-alt"></span>
                <?php _e('Create Workflow', 'db-app-builder'); ?>
            </button>
        </div>
    </div>

    <div class="dab-admin-content">
        <?php if ($action === 'edit' && $workflow_id > 0): ?>
            <!-- Workflow Editor -->
            <div id="workflow-editor" class="dab-workflow-editor">
                <div class="dab-workflow-editor-header">
                    <button type="button" class="dab-btn dab-btn-outline-secondary" id="back-to-list-btn">
                        <span class="dashicons dashicons-arrow-left-alt"></span>
                        <?php _e('Back to Workflows', 'db-app-builder'); ?>
                    </button>
                    <div class="dab-workflow-editor-actions">
                        <button type="button" class="dab-btn dab-btn-outline-primary" id="test-workflow-btn">
                            <span class="dashicons dashicons-controls-play"></span>
                            <?php _e('Test Workflow', 'db-app-builder'); ?>
                        </button>
                        <button type="button" class="dab-btn dab-btn-primary" id="save-workflow-btn">
                            <span class="dashicons dashicons-saved"></span>
                            <?php _e('Save Workflow', 'db-app-builder'); ?>
                        </button>
                    </div>
                </div>

                <div class="dab-workflow-editor-content">
                    <!-- Workflow Configuration Panel -->
                    <div class="dab-workflow-config-panel">
                        <h3><?php _e('Workflow Configuration', 'db-app-builder'); ?></h3>

                        <div class="dab-form-group">
                            <label for="workflow-name"><?php _e('Workflow Name', 'db-app-builder'); ?></label>
                            <input type="text" id="workflow-name" class="dab-form-control" placeholder="<?php _e('Enter workflow name', 'db-app-builder'); ?>">
                        </div>

                        <div class="dab-form-group">
                            <label for="workflow-description"><?php _e('Description', 'db-app-builder'); ?></label>
                            <textarea id="workflow-description" class="dab-form-control" rows="3" placeholder="<?php _e('Enter workflow description', 'db-app-builder'); ?>"></textarea>
                        </div>

                        <div class="dab-form-group">
                            <label for="trigger-type"><?php _e('Trigger Type', 'db-app-builder'); ?></label>
                            <select id="trigger-type" class="dab-form-control">
                                <option value=""><?php _e('Select trigger type', 'db-app-builder'); ?></option>
                                <option value="form_submit"><?php _e('Form Submission', 'db-app-builder'); ?></option>
                                <option value="data_change"><?php _e('Data Change', 'db-app-builder'); ?></option>
                                <option value="schedule"><?php _e('Schedule', 'db-app-builder'); ?></option>
                                <option value="webhook"><?php _e('Webhook', 'db-app-builder'); ?></option>
                                <option value="manual"><?php _e('Manual', 'db-app-builder'); ?></option>
                            </select>
                        </div>

                        <!-- Trigger Configuration -->
                        <div id="trigger-config" class="dab-trigger-config" style="display: none;">
                            <!-- Form Submit Config -->
                            <div id="form-submit-config" class="dab-trigger-config-section" style="display: none;">
                                <div class="dab-form-group">
                                    <label for="trigger-form"><?php _e('Select Form', 'db-app-builder'); ?></label>
                                    <select id="trigger-form" class="dab-form-control">
                                        <option value=""><?php _e('Select form', 'db-app-builder'); ?></option>
                                        <?php foreach ($forms as $form): ?>
                                            <option value="<?php echo esc_attr($form->id); ?>">
                                                <?php echo esc_html($form->form_title); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>

                            <!-- Data Change Config -->
                            <div id="data-change-config" class="dab-trigger-config-section" style="display: none;">
                                <div class="dab-form-group">
                                    <label for="trigger-table"><?php _e('Select Table', 'db-app-builder'); ?></label>
                                    <select id="trigger-table" class="dab-form-control">
                                        <option value=""><?php _e('Select table', 'db-app-builder'); ?></option>
                                        <?php foreach ($tables as $table): ?>
                                            <option value="<?php echo esc_attr($table->id); ?>">
                                                <?php echo esc_html($table->table_label); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="dab-form-group">
                                    <label for="change-type"><?php _e('Change Type', 'db-app-builder'); ?></label>
                                    <select id="change-type" class="dab-form-control">
                                        <option value="both"><?php _e('Create or Update', 'db-app-builder'); ?></option>
                                        <option value="create"><?php _e('Create Only', 'db-app-builder'); ?></option>
                                        <option value="update"><?php _e('Update Only', 'db-app-builder'); ?></option>
                                    </select>
                                </div>
                            </div>

                            <!-- Schedule Config -->
                            <div id="schedule-config" class="dab-trigger-config-section" style="display: none;">
                                <div class="dab-form-group">
                                    <label for="schedule-type"><?php _e('Schedule Type', 'db-app-builder'); ?></label>
                                    <select id="schedule-type" class="dab-form-control">
                                        <option value="hourly"><?php _e('Hourly', 'db-app-builder'); ?></option>
                                        <option value="daily"><?php _e('Daily', 'db-app-builder'); ?></option>
                                        <option value="weekly"><?php _e('Weekly', 'db-app-builder'); ?></option>
                                        <option value="monthly"><?php _e('Monthly', 'db-app-builder'); ?></option>
                                    </select>
                                </div>
                            </div>

                            <!-- Webhook Config -->
                            <div id="webhook-config" class="dab-trigger-config-section" style="display: none;">
                                <div class="dab-form-group">
                                    <label for="webhook-key"><?php _e('Webhook Key', 'db-app-builder'); ?></label>
                                    <input type="text" id="webhook-key" class="dab-form-control" placeholder="<?php _e('Auto-generated webhook key', 'db-app-builder'); ?>" readonly>
                                    <button type="button" class="dab-btn dab-btn-outline-secondary dab-btn-sm" id="generate-webhook-key">
                                        <?php _e('Generate New Key', 'db-app-builder'); ?>
                                    </button>
                                </div>
                                <div class="dab-form-group">
                                    <label><?php _e('Webhook URL', 'db-app-builder'); ?></label>
                                    <input type="text" id="webhook-url" class="dab-form-control" readonly>
                                    <small class="dab-form-text"><?php _e('Use this URL to trigger the workflow from external systems', 'db-app-builder'); ?></small>
                                </div>
                            </div>

                            <!-- Manual Config -->
                            <div id="manual-config" class="dab-trigger-config-section" style="display: none;">
                                <div class="dab-form-group">
                                    <label for="user-roles"><?php _e('User Roles', 'db-app-builder'); ?></label>
                                    <select id="user-roles" class="dab-form-control" multiple>
                                        <?php
                                        $roles = wp_roles()->get_names();
                                        foreach ($roles as $role_key => $role_name): ?>
                                            <option value="<?php echo esc_attr($role_key); ?>">
                                                <?php echo esc_html($role_name); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <small class="dab-form-text"><?php _e('Select which user roles can manually trigger this workflow', 'db-app-builder'); ?></small>
                                </div>
                            </div>
                        </div>

                        <div class="dab-form-group">
                            <label class="dab-checkbox-label">
                                <input type="checkbox" id="workflow-active" checked>
                                <?php _e('Active', 'db-app-builder'); ?>
                            </label>
                        </div>
                    </div>

                    <!-- Workflow Canvas -->
                    <div class="dab-workflow-canvas">
                        <h3><?php _e('Workflow Steps', 'db-app-builder'); ?></h3>
                        <div id="workflow-canvas" class="dab-canvas">
                            <div class="dab-canvas-placeholder">
                                <span class="dashicons dashicons-plus-alt"></span>
                                <p><?php _e('Drag actions from the sidebar to build your workflow', 'db-app-builder'); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Action Palette -->
                    <div class="dab-action-palette">
                        <h3><?php _e('Actions', 'db-app-builder'); ?></h3>
                        <div class="dab-action-list">
                            <div class="dab-action-item" data-action="send_email">
                                <span class="dashicons dashicons-email-alt"></span>
                                <span><?php _e('Send Email', 'db-app-builder'); ?></span>
                            </div>
                            <div class="dab-action-item" data-action="create_record">
                                <span class="dashicons dashicons-plus-alt"></span>
                                <span><?php _e('Create Record', 'db-app-builder'); ?></span>
                            </div>
                            <div class="dab-action-item" data-action="update_record">
                                <span class="dashicons dashicons-edit"></span>
                                <span><?php _e('Update Record', 'db-app-builder'); ?></span>
                            </div>
                            <div class="dab-action-item" data-action="delete_record">
                                <span class="dashicons dashicons-trash"></span>
                                <span><?php _e('Delete Record', 'db-app-builder'); ?></span>
                            </div>
                            <div class="dab-action-item" data-action="api_call">
                                <span class="dashicons dashicons-cloud"></span>
                                <span><?php _e('API Call', 'db-app-builder'); ?></span>
                            </div>
                            <div class="dab-action-item" data-action="condition">
                                <span class="dashicons dashicons-randomize"></span>
                                <span><?php _e('Condition', 'db-app-builder'); ?></span>
                            </div>
                            <div class="dab-action-item" data-action="delay">
                                <span class="dashicons dashicons-clock"></span>
                                <span><?php _e('Delay', 'db-app-builder'); ?></span>
                            </div>
                            <div class="dab-action-item" data-action="calculate">
                                <span class="dashicons dashicons-calculator"></span>
                                <span><?php _e('Calculate', 'db-app-builder'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <?php else: ?>
            <!-- Workflows List -->
            <div class="dab-workflows-list">
                <?php if (empty($workflows)): ?>
                    <div class="dab-empty-state">
                        <div class="dab-empty-state-icon">
                            <span class="dashicons dashicons-networking"></span>
                        </div>
                        <h3><?php _e('No Workflows Yet', 'db-app-builder'); ?></h3>
                        <p><?php _e('Create your first workflow to automate your business processes.', 'db-app-builder'); ?></p>
                        <button type="button" class="dab-btn dab-btn-primary" id="create-first-workflow-btn">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php _e('Create Your First Workflow', 'db-app-builder'); ?>
                        </button>
                    </div>
                <?php else: ?>
                    <div class="dab-workflows-grid">
                        <?php foreach ($workflows as $workflow): ?>
                            <div class="dab-workflow-card" data-workflow-id="<?php echo esc_attr($workflow->id); ?>">
                                <div class="dab-workflow-card-header">
                                    <h4><?php echo esc_html($workflow->name); ?></h4>
                                    <div class="dab-workflow-status">
                                        <?php if ($workflow->is_active): ?>
                                            <span class="dab-status-badge dab-status-active"><?php _e('Active', 'db-app-builder'); ?></span>
                                        <?php else: ?>
                                            <span class="dab-status-badge dab-status-inactive"><?php _e('Inactive', 'db-app-builder'); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="dab-workflow-card-body">
                                    <p><?php echo esc_html($workflow->description ?: __('No description', 'db-app-builder')); ?></p>
                                    <div class="dab-workflow-meta">
                                        <span class="dab-workflow-trigger">
                                            <span class="dashicons dashicons-controls-play"></span>
                                            <?php
                                            $safe_str_replace = function_exists('dab_safe_str_replace') ? 'dab_safe_str_replace' : 'str_replace';
                                            echo esc_html(ucfirst($safe_str_replace('_', ' ', $workflow->trigger_type)));
                                            ?>
                                        </span>
                                        <span class="dab-workflow-created">
                                            <?php echo esc_html(human_time_diff(strtotime($workflow->created_at), current_time('timestamp')) . ' ago'); ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="dab-workflow-card-actions">
                                    <button type="button" class="dab-btn dab-btn-outline-primary dab-btn-sm edit-workflow-btn" data-workflow-id="<?php echo esc_attr($workflow->id); ?>">
                                        <span class="dashicons dashicons-edit"></span>
                                        <?php _e('Edit', 'db-app-builder'); ?>
                                    </button>
                                    <button type="button" class="dab-btn dab-btn-outline-secondary dab-btn-sm duplicate-workflow-btn" data-workflow-id="<?php echo esc_attr($workflow->id); ?>">
                                        <span class="dashicons dashicons-admin-page"></span>
                                        <?php _e('Duplicate', 'db-app-builder'); ?>
                                    </button>
                                    <button type="button" class="dab-btn dab-btn-outline-danger dab-btn-sm delete-workflow-btn" data-workflow-id="<?php echo esc_attr($workflow->id); ?>">
                                        <span class="dashicons dashicons-trash"></span>
                                        <?php _e('Delete', 'db-app-builder'); ?>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Workflow Creation Modal -->
<div id="workflow-creation-modal" class="dab-modal" style="display: none;">
    <div class="dab-modal-content">
        <div class="dab-modal-header">
            <h3><?php _e('Create New Workflow', 'db-app-builder'); ?></h3>
            <button type="button" class="dab-modal-close">&times;</button>
        </div>
        <div class="dab-modal-body">
            <div class="dab-form-group">
                <label for="new-workflow-name"><?php _e('Workflow Name', 'db-app-builder'); ?></label>
                <input type="text" id="new-workflow-name" class="dab-form-control" placeholder="<?php _e('Enter workflow name', 'db-app-builder'); ?>">
            </div>
            <div class="dab-form-group">
                <label for="new-workflow-description"><?php _e('Description', 'db-app-builder'); ?></label>
                <textarea id="new-workflow-description" class="dab-form-control" rows="3" placeholder="<?php _e('Enter workflow description', 'db-app-builder'); ?>"></textarea>
            </div>
        </div>
        <div class="dab-modal-footer">
            <button type="button" class="dab-btn dab-btn-outline-secondary" id="cancel-workflow-creation">
                <?php _e('Cancel', 'db-app-builder'); ?>
            </button>
            <button type="button" class="dab-btn dab-btn-primary" id="confirm-workflow-creation">
                <?php _e('Create Workflow', 'db-app-builder'); ?>
            </button>
        </div>
    </div>
</div>

<!-- Step Configuration Modal -->
<div id="step-config-modal" class="dab-modal" style="display: none;">
    <div class="dab-modal-content">
        <div class="dab-modal-header">
            <h3 id="step-config-modal-title"><?php _e('Configure Step', 'db-app-builder'); ?></h3>
            <button type="button" class="dab-step-modal-close">&times;</button>
        </div>
        <div class="dab-modal-body">
            <div id="step-config-form">
                <!-- Dynamic form content will be inserted here -->
            </div>
        </div>
        <div class="dab-modal-footer">
            <button type="button" class="dab-btn dab-btn-outline-secondary" id="cancel-step-config">
                <?php _e('Cancel', 'db-app-builder'); ?>
            </button>
            <button type="button" class="dab-btn dab-btn-primary" id="save-step-config">
                <?php _e('Save Configuration', 'db-app-builder'); ?>
            </button>
        </div>
    </div>
</div>

<script>
// Pass data to JavaScript
window.dabWorkflowData = {
    ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
    nonce: '<?php echo wp_create_nonce('dab_workflow_nonce'); ?>',
    tables: <?php echo json_encode($tables); ?>,
    forms: <?php echo json_encode($forms); ?>,
    currentWorkflowId: <?php echo $workflow_id; ?>,
    isEditMode: <?php echo $action === 'edit' ? 'true' : 'false'; ?>
};
</script>

<?php
// Enqueue workflow builder assets
wp_enqueue_style('dab-workflow-builder', plugin_dir_url(dirname(__FILE__)) . 'assets/css/workflow-builder.css', array(), DAB_VERSION);
wp_enqueue_script('dab-workflow-builder', plugin_dir_url(dirname(__FILE__)) . 'assets/js/workflow-builder.js', array('jquery', 'jquery-ui-sortable', 'jquery-ui-draggable', 'jquery-ui-droppable'), DAB_VERSION, true);
?>
